# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_acerta
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-20 13:30+0000\n"
"PO-Revision-Date: 2025-03-20 13:30+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_hr_payroll_acerta
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_acerta.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_acerta.res_config_settings_view_form
msgid "Acerta"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_res_company__acerta_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_res_config_settings__acerta_code
msgid "Acerta Affiliation Number"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_l10n_be_hr_payroll_export_acerta_employee
msgid "Acerta Export Employee"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_hr_contract__acerta_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_hr_work_entry_type__acerta_code
msgid "Acerta code"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/hr_payroll_export_acerta.py:0
msgid "Acerta code is missing for contract: %(contract)s"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/hr_payroll_export_acerta.py:0
msgid "Acerta code is missing for work entry type %(work_entry_type)s"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_acerta.res_config_settings_view_form
msgid "Allow to export Working Entries to your Social Secretariat"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:hr.leave.type,name:l10n_be_hr_payroll_acerta.holiday_type_small_unemployment_birth
#: model:hr.work.entry.type,name:l10n_be_hr_payroll_acerta.work_entry_type_small_unemployment_birth
msgid "Brief Holiday (Birth)"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__company_id
msgid "Company"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__contract_ids
msgid "Contract"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__create_date
msgid "Created on"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__eligible_employee_line_ids
msgid "Eligible Employees"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__eligible_employee_count
msgid "Eligible Employees Count"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__employee_id
msgid "Employee"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__export_id
msgid "Export"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__export_file
msgid "Export File"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__export_filename
msgid "Export Filename"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_l10n_be_hr_payroll_export_acerta
msgid "Export Payroll to Acerta"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.ui.menu,name:l10n_be_hr_payroll_acerta.menu_l10n_be_export_work_entries_acerta
msgid "Export Work Entries to Acerta"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/hr_payroll_export_acerta.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_acerta.l10n_be_export_acerta_action
msgid "Export to Acerta"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model,name:l10n_be_hr_payroll_acerta.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__id
msgid "ID"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__period_start
msgid "Period Start"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__period_stop
msgid "Period Stop"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__reference_month
msgid "Reference Month"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta__reference_year
msgid "Reference Year"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/res_company.py:0
msgid "The code should be 7 characters!"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/hr_contract.py:0
msgid ""
"The following contracts have an Acerta code that is too long: %(contracts)s"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#. odoo-python
#: code:addons/l10n_be_hr_payroll_acerta/models/hr_work_entry_type.py:0
msgid ""
"The following work entry types have an Acerta code thatis not between 2 and "
"7 characters: %(work_entries)s"
msgstr ""

#. module: l10n_be_hr_payroll_acerta
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_acerta.field_l10n_be_hr_payroll_export_acerta_employee__work_entry_ids
msgid "Work Entry"
msgstr ""
