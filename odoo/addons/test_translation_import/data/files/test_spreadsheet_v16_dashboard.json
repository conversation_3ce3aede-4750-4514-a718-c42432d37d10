{"version": 16, "sheets": [{"id": "Sheet1", "name": "Sheet1", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {}, "merges": [], "cells": {}, "conditionalFormats": [], "figures": [{"id": "1", "x": 0, "y": 0, "width": 100, "height": 100, "tag": "chart", "data": {"type": "bar", "dataSetsHaveTitle": false, "background": "#FFFFFF", "dataSets": ["A4"], "legendPosition": "none", "verticalAxisPosition": "left", "title": {"text": "Bar chart title"}, "stackedBar": false, "axesDesign": {"x": {"title": {"text": "Chart horizontal axis title"}}, "y": {"title": {"text": "Chart vertical axis title"}}}}}, {"id": "2", "x": 0, "y": 0, "width": 100, "height": 100, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": {"text": "Scorecard title"}, "type": "scorecard", "background": "#FFFFFF", "keyValue": "A5"}}, {"id": "3", "x": 541, "y": 61, "width": 536, "height": 335, "tag": "chart", "data": {"title": {"text": "Opportunities"}, "axesDesign": {"x": {"title": {"text": "Odoo Chart horizontal axis title"}}, "y": {"title": {"text": "Odoo Chart vertical axis title"}}}, "id": "3", "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": [], "measure": "__count", "resModel": "crm.lead"}, "searchParams": {"comparison": null, "domain": [], "groupBy": [], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}], "areGridLinesVisible": true, "isVisible": true}], "odooVersion": 4, "lists": {}, "pivots": {}}