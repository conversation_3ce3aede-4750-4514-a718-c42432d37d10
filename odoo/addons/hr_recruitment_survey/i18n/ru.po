# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_survey
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_recruitment_survey
#: model:mail.template,body_html:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'applicant'\">[applicant name]</t><br/><br/>\n"
"        <t>\n"
"            You've progressed through the recruitment process and we would like you to answer some questions.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t>\n"
"                    Start the written interview\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the interview for <t t-out=\"format_date(object.deadline)\">[deadline date]</t>.<br/><br/>\n"
"        </t>\n"
"        <t>\n"
"            We wish you good luck! Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_recruitment_survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Recruitment"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Consult</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""
"<span class=\"o_stat_text\">Проконсультируйтесь</span>\n"
"                        <span class=\"o_stat_text\">Интервью</span>"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr "О вас"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr "Активность"

#. module: hr_recruitment_survey
#: model_terms:ir.actions.act_window,help:hr_recruitment_survey.survey_survey_action_recruitment
msgid "Add a new survey"
msgstr "Aggiungere un nuovo sondaggio"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_invite__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_user_input__applicant_id
msgid "Applicant"
msgstr "Кандидат"

#. module: hr_recruitment_survey
#: model:mail.template,name:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Applicant: Interview"
msgstr "Соискатель: Интервью"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr ""
"Выберите форму собеседования для этой вакансии, и вы сможете "
"распечатать/ответить на это собеседование всех кандидатов, претендующих на "
"эту должность"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr "Форма для интервью на дисплее"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr "Образование"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate?"
msgstr "Какой университет вы закончили или собираетесь закончить?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr "Ладить с коллегами"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr "Получение льгот, таких как бесплатная парковка, абонементы в спортзал"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr "Хорошая зарплата"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr "Приятная обстановка в офисе"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr "Бесплатные подарки, такие как чай, кофе и канцелярские принадлежности"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr "Важно"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interview Form"
msgstr "Форма для собеседования"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
msgid "Interview Form: %s"
msgstr "Форма интервью: %s"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Survey"
msgstr "Интервью Опрос"

#. module: hr_recruitment_survey
#: model:ir.actions.act_window,name:hr_recruitment_survey.survey_survey_action_recruitment
#: model:ir.ui.menu,name:hr_recruitment_survey.menu_hr_recruitment_config_surveys
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interviews"
msgstr "Интервью"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_survey__hr_job_ids
msgid "Job Position"
msgstr "Должность"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr "Знание"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr "Качество управления"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr "Не важно"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr "Расположение офиса"

#. module: hr_recruitment_survey
#: model:mail.template,subject:hr_recruitment_survey.mail_template_applicant_interview_invite
msgid "Participate to {{ object.survey_id.display_name }} interview"
msgstr "Примите участие в интервью {{ object.survey_id.display_name }}"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr "Опыт работы в прошлом"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid ""
"Please answer those questions to help recruitment officers to preprocess "
"your application."
msgstr ""
"Пожалуйста, ответьте на эти вопросы, чтобы помочь специалистам по подбору "
"персонала подготовить ваше заявление."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application."
msgstr ""
"Пожалуйста, заполните информацию о себе: кто вы, какое у вас образование, опыт и деятельность.\n"
"    Это поможет нам обработать вашу заявку."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Please provide an applicant name."
msgstr "Пожалуйста, укажите имя заявителя."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid ""
"Please summarize your education history: schools, location, diplomas, ..."
msgstr ""
"Пожалуйста, кратко опишите историю вашего образования: школы, "
"местоположение, дипломы, ..."

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid ""
"Please tell us a bit more about yourself: what are your main activities, ..."
msgstr ""
"Пожалуйста, расскажите нам немного больше о себе: каковы ваши основные "
"занятия, ..."

#. module: hr_recruitment_survey
#: model:ir.model.fields.selection,name:hr_recruitment_survey.selection__survey_survey__survey_type__recruitment
msgid "Recruitment"
msgstr "Найм"

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr "Анкета для набора персонала"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_ids
msgid "Responses"
msgstr "Ответы"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "See interview report"
msgstr "Смотрите отчет об интервью"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "Send Interview"
msgstr "Отправить интервью"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
msgid "Send an interview"
msgstr "Отправить интервью"

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#: model:ir.model,name:hr_recruitment_survey.model_survey_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
msgid "Survey"
msgstr "Опрос"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Мастер приглашений на опрос"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Тип опроса"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Опрос пользователей ввод"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description_done:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""
"Спасибо, что ответили на этот опрос. Мы вернемся к вам в ближайшее время."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/models/survey_user_input.py:0
msgid "The applicant \"%s\" has finished the survey."
msgstr "Заявитель \"%s\" завершил опрос."

#. module: hr_recruitment_survey
#. odoo-python
#: code:addons/hr_recruitment_survey/wizard/survey_invite.py:0
msgid "The survey %(survey_link)s has been sent to %(partner_link)s"
msgstr "Опрос %(survey_link)s был отправлен на %(partner_link)s"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr "Очень важно"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Were you referred by an employee?"
msgstr "Вас направил сотрудник?"

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "What are your main knowledge regarding the job you are applying to?"
msgstr "Каковы ваши основные знания о работе, на которую вы претендуете?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you?"
msgstr "Что для вас важно?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from?"
msgstr "Из какой вы страны?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr "Работа с современными технологиями"

#. module: hr_recruitment_survey
#: model_terms:ir.actions.act_window,help:hr_recruitment_survey.survey_survey_action_recruitment
msgid ""
"You can create surveys used for recruitments. Design easily your interview,\n"
"                send invitations and analyze answers."
msgstr ""
