# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_purchase
# 
# Translators:
# Wil <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_bricks_product_template
msgid "Bricks"
msgstr "Tijolos"

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_cement_product_template
msgid "Cement"
msgstr ""

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid "No purchase order found. Let's create one."
msgstr ""

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid ""
"Once you ordered your products from your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_project_project
#: model:ir.model.fields,field_description:project_purchase.field_purchase_order__project_id
msgid "Project"
msgstr "Projeto"

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Notas de encomenda"

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
msgid "Purchase Order Items"
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Linha de Encomenda"

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
#: model:ir.embedded.actions,name:project_purchase.project_embedded_action_purchase_orders
msgid "Purchase Orders"
msgstr "Notas de encomenda"

#. module: project_purchase
#: model:product.template,name:project_purchase.product_product_sand_product_template
msgid "Sand"
msgstr ""
