<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="payment_transaction_form" model="ir.ui.view">
        <field name="name">payment.transaction.form</field>
        <field name="model">payment.transaction</field>
        <field name="inherit_id" ref="payment.payment_transaction_form"/>
        <field name="arch" type="xml">
            <button name="action_view_refunds" position="before">
                <field name="pos_order_id" invisible="1"/>
                <button name="action_view_pos_order" type="object"
                        class="oe_stat_button" icon="fa-shopping-cart"
                        invisible="not pos_order_id">
                    <field name="pos_order_id" widget="statinfo" string="POS Order"/>
                </button>
            </button>
        </field>
    </record>
</odoo>
