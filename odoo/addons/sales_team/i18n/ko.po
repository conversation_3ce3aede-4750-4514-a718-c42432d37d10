# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sales_team
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# Sarah <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope me-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>New</span>"
msgstr "<span>신규</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>Reporting</span>"
msgstr "<span>보고</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>View</span>"
msgstr "<span>보기</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__active
msgid "Active"
msgstr "활성화"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "활동 유형"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Add a Team Member"
msgstr "팀원 추가"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__crm_team_member_ids
msgid ""
"Add members to automatically assign their documents to this sales team."
msgstr "영업팀에 문서를 자동으로 배정하려면 팀원을 추가하세요."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
msgid ""
"Adding %(user_name)s in this team will remove them from %(team_names)s. "
"Working in multiple teams? Activate the option under Configuration>Settings."
msgstr ""
"%(user_name)s 님을 이 팀에 추가할 경우, %(team_names)s 팀에서는 제거됩니다. 여러 개의 팀에서 작업 중인가요? "
"환경설정 > 설정에서 메뉴를 활성화하세요."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid ""
"Adding %(user_names)s in this team will remove them from %(team_names)s."
msgstr "이 팀에 %(user_names)s을 추가하면 %(team_names)s에서 해당 사용자가 제거됩니다."

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Administrator"
msgstr "관리자"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Archived"
msgstr "보관됨"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "아바타"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Big Pretty Button :)"
msgstr "크고 아름다운 버튼 :)"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_tag
msgid "CRM Tag"
msgstr "CRM 태그"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Cannot delete default team \"%s\""
msgstr "\"%s\" 기본 팀을 삭제할 수 없습니다"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__color
msgid "Color"
msgstr "색상"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "색상표"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__company_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Company"
msgstr "회사"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "Configuration"
msgstr "설정"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor7
msgid "Consulting"
msgstr "컨설팅"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid "Create CRM Tags"
msgstr "CRM 태그를 생성합니다."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid "Create a Sales Team"
msgstr "영업부 추가"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid "Create an Activity Type"
msgstr "업무 유형 생성"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_uid
msgid "Created by"
msgstr "작성자"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_date
msgid "Created on"
msgstr "작성일자"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "통화"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "현황판 버튼"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "현황판 그래프 데이터"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid "Define a new sales team"
msgstr "새로운 영업팀 정의"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor5
msgid "Design"
msgstr "디자인"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__display_name
msgid "Display Name"
msgstr "표시명"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__email
msgid "Email"
msgstr "이메일"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "즐겨찾는 회원"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr "즐겨찾는 팀이 현황판에 표시되고 쉽게 접근할 수 있습니다."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Group By"
msgstr "그룹별"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Group By..."
msgstr "그룹별..."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__has_message
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,help:sales_team.field_crm_team_member__is_membership_multi
msgid ""
"If True, users may belong to several sales teams. Otherwise membership is "
"limited to a single sales team."
msgstr "True인 경우 여러 개의 영업팀에 배치할 수 있습니다. 그렇지 않은 경우 소속이 하나의 영업팀으로 제한됩니다."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr "활성 필드를 false로 설정하면, 영업팀을 제거하지 않아도 필드를 숨길 수 있습니다."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_1920
msgid "Image"
msgstr "이미지"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_128
msgid "Image (128)"
msgstr "이미지 (128)"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor4
msgid "Information"
msgstr "정보"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr "주 사용자인 영업팀입니다. 특히 파이프라인을 관리하거나 영업팀을 청구서 발행 또는 구독 업무에서 설정할 때 사용합니다."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_company_ids
msgid "Member Company"
msgstr "회원사"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__member_warning
msgid "Member Warning"
msgstr "회원 경고"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Members"
msgstr "팀원"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_warning
msgid "Membership Issue Warning"
msgstr "멤버십 문제 경고"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_ids
msgid "Messages"
msgstr "메시지"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__mobile
msgid "Mobile"
msgstr "휴대폰 번호"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__is_membership_multi
msgid "Multiple Memberships Allowed"
msgstr "여러 개의 멤버십 유지 가능"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__name
msgid "Name"
msgstr "이름"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor8
msgid "Other"
msgstr "기타"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__phone
msgid "Phone"
msgstr "전화번호"

#. module: sales_team
#: model:crm.team,name:sales_team.pos_sales_team
msgid "Point of Sale"
msgstr "POS"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Pre-Sales"
msgstr "판매 전"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor1
msgid "Product"
msgstr "품목"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__rating_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Sales"
msgstr "판매"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_tree
msgid "Sales Men"
msgstr "영업 담당자"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Sales Person"
msgstr "영업 담당자"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__crm_team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "영업팀"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team_member
msgid "Sales Team Member"
msgstr "영업 팀 구성원"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_ids
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_member_ids
msgid "Sales Team Members"
msgstr "영업팀 멤버"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_all_ids
msgid "Sales Team Members (incl. inactive)"
msgstr "영업팀 멤버 (비활동 멤버 포함)"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "영업팀"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_id
msgid "Salesperson"
msgstr "영업 담당자"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Salespersons"
msgstr "영업 담당자"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "영업팀 검색"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Sample data"
msgstr "예제 데이터"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__sequence
msgid "Sequence"
msgstr "순서"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor3
msgid "Services"
msgstr "서비스"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "현황판에 표시"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor2
msgid "Software"
msgstr "소프트웨어"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__name
msgid "Tag Name"
msgstr "태그 이름"

#. module: sales_team
#: model:ir.model.constraint,message:sales_team.constraint_crm_tag_name_uniq
msgid "Tag name already exists!"
msgstr "태그명이 이미 존재합니다!"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_crm_tag_action
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_tree
msgid "Tags"
msgstr "태그"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Details"
msgstr "팀 정보"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Team Leader"
msgstr "팀장"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_member_action
msgid "Team Members"
msgstr "팀 구성원"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Team Members are salespersons assigned to specific teams."
msgstr "팀 멤버는 특정 팀에 소속된 영업사원을 말합니다."

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_pipeline
msgid "Teams"
msgstr "팀"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "채널의 색상"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__company_id
msgid "The default company for this user."
msgstr "해당 사용자의 회사 기본값입니다."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Prepare meeting\")."
msgstr "이는 수행해야 하는 작업을 다양한 카테고리로 분류한 것입니다(예: '전화' 또는 '회의 준비')."

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor6
msgid "Training"
msgstr "훈련"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_in_teams_ids
msgid ""
"UX: Give users not to add in the currently chosen team to avoid duplicates"
msgstr "UX: 중복되지 않도록 사용자가 현재 선택한 팀에 추가하지 않도록 합니다."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_company_ids
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_company_ids
msgid "UX: Limit to team company or all if no company"
msgstr "UX: 팀이 속한 회사로 제한하거나 회사가 없는 경우 전체 회사로 둡니다"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid "Undefined graph model for Sales Team: %s"
msgstr "영업팀에 대한 정의되지 않은 그래프 모델 : %s"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid ""
"Use Sales Teams to organize your sales departments and draw up reports."
msgstr "영업팀 기능으로 영업 부서를 체계화하고 보고서를 작성하십시오."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                Each team will work with a separate pipeline."
msgstr ""
"영업팀 기능으로 영업 부서를 체계화합니다.\n"
"           각각의 팀에서는 별도의 파이프라인으로 업무를 진행합니다."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid ""
"Use Tags to manage and track your Opportunities (product structure, sales "
"type, ...)"
msgstr "영업기회 관리와 추적에 태그를 사용해 보세요 (품목 구조, 판매 유형 등)."

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "User"
msgstr "사용자"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_company_ids
msgid "User Company"
msgstr "사용자 회사"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_in_teams_ids
msgid "User In Teams"
msgstr "팀내 사용자"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User Sales Team"
msgstr "사용자 영업팀 "

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "사용자 : 모든 문서"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "사용자 : 자신의 문서만"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_ids
msgid "Users assigned to this team."
msgstr "이 팀에 소속되어 있는 사용자입니다. "

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
msgid "Website"
msgstr "웹사이트"

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team.py:0
msgid ""
"Working in multiple teams? Activate the option under Configuration>Settings."
msgstr "여러 팀에서 작업해야 하나요? 환경설정 > 설정에서 이 옵션을 활성화하세요."

#. module: sales_team
#. odoo-python
#: code:addons/sales_team/models/crm_team_member.py:0
msgid ""
"You are trying to create duplicate membership(s). We found that "
"%(duplicates)s already exist(s)."
msgstr "중복된 멤버십입니다. %(duplicates)s이(가) 이미 존재합니다. "

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "e.g. North America"
msgstr "예: 북미지역팀"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
msgid "e.g. Services"
msgstr "예. 서비스"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr "사용자는 영업 응용 프로그램의 모든 사람의 모든 레코드에 접근할 수 있습니다."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr "사용자는 영업 응용 프로그램에서 자신의 데이터에 접근할 수 있습니다."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr "사용자는 통계 보고서뿐만 아니라 판매 환경 설정에 접근할 수 있습니다."
