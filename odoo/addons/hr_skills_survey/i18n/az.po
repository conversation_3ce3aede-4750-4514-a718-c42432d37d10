# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_survey
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_valid
msgid "AWS Cloud"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__survey_id
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__display_type__certification
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Certification"
msgstr ""

#. module: hr_skills_survey
#: model:ir.ui.menu,name:hr_skills_survey.hr_employee_certication_report_menu
msgid "Certifications"
msgstr "Sertifikatlar"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Department"
msgstr "Şöbə"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Göstəriləcək Form"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Employee"
msgstr "İşçi"

#. module: hr_skills_survey
#: model:ir.actions.act_window,name:hr_skills_survey.hr_employee_certification_report_action
msgid "Employee Certifications"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__expiration_status
msgid "Expiration Status"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiration date"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expired
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expired"
msgstr "Vaxtı bitmiş"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expiring
msgid "Expiring"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiring Soon"
msgstr ""

#. module: hr_skills_survey
#: model:hr.resume.line.type,name:hr_skills_survey.resume_type_certification
msgid "Internal Certification"
msgstr ""

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_expiring
msgid "MongoDB Developer"
msgstr ""

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_aws
msgid "Oracle DB"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_hr_resume_line
msgid "Resume line of an employee"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields,help:hr_skills_survey.field_survey_survey__certification_validity_months
msgid ""
"Specify the number of months the certification is valid after being awarded."
" Enter 0 for certifications that never expire."
msgstr ""

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_survey
msgid "Survey"
msgstr "Sorğu"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__valid
msgid "Valid"
msgstr "Düzgün"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Valid Until"
msgstr "Etibarlılıq Müddəti"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_survey_survey__certification_validity_months
msgid "Validity"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity End"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity Start"
msgstr ""
