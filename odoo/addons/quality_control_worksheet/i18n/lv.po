# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_control_worksheet
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.quality_check_view_form_inherit_worksheet
msgid "Check Worksheet"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_7
msgid "Comments"
msgstr "Komentāri"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_2
msgid "Created by"
msgstr "Izveidoja"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_1
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_ui_view_3
msgid "Created on"
msgstr "Izveidots"

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.report_custom_x_quality_check_worksheet_template_2
msgid "Date"
msgstr "Datums"

#. module: quality_control_worksheet
#. odoo-javascript
#: code:addons/quality_control_worksheet/static/src/views/worksheet_validation_controller.xml:0
msgid "Discard"
msgstr "Atmest"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_3
msgid "Display Name"
msgstr "Nosaukums"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_4
msgid "ID"
msgstr "ID"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_6
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_5
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.report_custom_x_quality_check_worksheet_template_2
msgid "Length"
msgstr "Garums"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_point__worksheet_model_name
msgid "Model Name"
msgstr "Modeļa nosaukums"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_8
msgid "Name"
msgstr "Nosaukums"

#. module: quality_control_worksheet
#. odoo-javascript
#: code:addons/quality_control_worksheet/static/src/views/worksheet_validation_controller.xml:0
msgid "Next"
msgstr "Nākamais"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_9
msgid "Passed"
msgstr "Nokārtots"

#. module: quality_control_worksheet
#. odoo-python
#: code:addons/quality_control_worksheet/models/quality.py:0
msgid "Please fill in the worksheet."
msgstr ""

#. module: quality_control_worksheet
#. odoo-javascript
#: code:addons/quality_control_worksheet/static/src/views/worksheet_validation_controller.xml:0
msgid "Previous"
msgstr "Iepriekšējais"

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.report_custom_x_quality_check_worksheet_template_2
msgid "Product"
msgstr "Prece"

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.model_quality_check
#: model:ir.model.fields,field_description:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_fields_10
msgid "Quality Check"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.model_quality_point
msgid "Quality Control Point"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_model_1
msgid "Quality Issues"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_check__worksheet_template_id
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_check_wizard__worksheet_template_id
msgid "Quality Template"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.model_report_quality_control_quality_worksheet
msgid "Quality Worksheet Report"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.actions.act_window,name:quality_control_worksheet.quality_control_worksheet_template_action_settings
#: model:ir.ui.menu,name:quality_control_worksheet.settings_worksheet_template
msgid "Quality Worksheet Templates"
msgstr ""

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.report_custom_x_quality_check_worksheet_template_2
msgid "Responsible"
msgstr "Atbildīgais"

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_point__worksheet_success_conditions
msgid "Success Conditions"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_point__worksheet_template_id
msgid "Template"
msgstr "Sagatave"

#. module: quality_control_worksheet
#. odoo-javascript
#: code:addons/quality_control_worksheet/static/src/views/worksheet_validation_controller.xml:0
msgid "Validate"
msgstr "Pārbaudīt"

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.model_quality_check_wizard
msgid "Wizard for Quality Check Pop Up"
msgstr ""

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.report_custom_x_quality_check_worksheet_template_2
msgid "Wood Texture"
msgstr ""

#. module: quality_control_worksheet
#: model:quality.point.test_type,name:quality_control_worksheet.test_type_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.quality_check_view_form_inherit_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.worksheet_custom_page
msgid "Worksheet"
msgstr ""

#. module: quality_control_worksheet
#: model_terms:ir.ui.view,arch_db:quality_control_worksheet.quality_check_view_form_inherit_worksheet
msgid "Worksheet Completed"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model.fields,field_description:quality_control_worksheet.field_quality_check__worksheet_count
msgid "Worksheet Count"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.model,name:quality_control_worksheet.model_worksheet_template
msgid "Worksheet Template"
msgstr ""

#. module: quality_control_worksheet
#: model:ir.actions.act_window,name:quality_control_worksheet.x_quality_check_worksheet_template_2_ir_actions_act_window_1
msgid "Worksheets"
msgstr ""
