<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_employee_form" model="ir.ui.view">
        <field name="name">hr.employee.form.inherit.l10n_au_hr_payroll</field>
        <field name="model">hr.employee</field>
        <field name="priority" eval="110"/>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="after">
                <div
                    class="alert alert-warning mb-0" role="alert"
                    invisible="not super_account_warning or company_country_code != 'AU'" groups="hr.group_hr_user">
                    <field name="super_account_warning" />
                </div>
            </xpath>
            <xpath expr="//label[@for='private_street']" position="before">
                <field name='l10n_au_other_names' invisible="company_country_code != 'AU'"/>
            </xpath>
            <page name="hr_payroll" position="inside">
                <group name="au_payroll" string="Australian Payroll">
                    <group name="l10n_au_personal" string="Personal Information" invisible="company_country_code != 'AU'">
                        <field name="is_non_resident"/>
                        <field name="l10n_au_tax_free_threshold" invisible="is_non_resident"/>
                        <field name="l10n_au_tfn_declaration"/>
                        <field name="l10n_au_tfn"
                        readonly="l10n_au_tfn_declaration != 'provided'"
                        required="l10n_au_tfn_declaration == 'provided'"
                        placeholder="*********"/>
                        <field name="l10n_au_abn" invisible="l10n_au_tax_treatment_category != 'V'"/>
                        <field name="l10n_au_previous_payroll_id" groups="base.group_no_one" />
                        <field name="l10n_au_payroll_id" groups="base.group_no_one" />
                    </group>
                    <group name="l10n_au_info" string="Tax and Contributions" invisible="company_country_code != 'AU'">
                        <field name="l10n_au_employment_basis_code" />
                        <field name="l10n_au_income_stream_type" />
                        <field name="l10n_au_work_country_id" invisible="l10n_au_income_stream_type != 'FEI'" />
                        <field name="l10n_au_tax_treatment_category" />
                        <field name="l10n_au_tax_treatment_option_actor"
                            invisible="l10n_au_tax_treatment_category != 'A'"
                            required="l10n_au_tax_treatment_category == 'A'"/>
                        <field name="l10n_au_less_than_3_performance"
                            invisible="l10n_au_tax_treatment_category != 'A' or l10n_au_tax_treatment_option_actor != 'D'"/>
                        <field name="l10n_au_tax_treatment_option_voluntary"
                            invisible="l10n_au_tax_treatment_category != 'V'"
                            required="l10n_au_tax_treatment_category == 'V'"/>
                        <field name="l10n_au_tax_treatment_option_seniors"
                            invisible="l10n_au_tax_treatment_category != 'S'"
                            required="l10n_au_tax_treatment_category == 'S'"/>
                        <field name="l10n_au_comissioners_installment_rate"
                            invisible="l10n_au_tax_treatment_category != 'V' or l10n_au_tax_treatment_option_voluntary != 'C'"/>
                        <div class="row text-danger" colspan="2" invisible="l10n_au_tax_treatment_category != 'V' or l10n_au_tax_treatment_option_voluntary != 'C' or l10n_au_comissioners_installment_rate != 0">
                            <span>
                                <p>The commissioner's instalment rate cannot be 0%. 20% will be applied instead.</p>
                            </span>
                        </div>
                        <field name="l10n_au_training_loan"/>
                        <field name="l10n_au_medicare_variation_form" filename="l10n_au_medicare_variation_form_filename" widget="binary" invisible="l10n_au_employment_basis_code == 'C'"/>
                        <field name="l10n_au_medicare_surcharge" invisible="not l10n_au_medicare_variation_form or l10n_au_employment_basis_code == 'C'"/>
                        <field name="l10n_au_medicare_exemption" invisible="not l10n_au_medicare_variation_form or not l10n_au_tax_free_threshold or l10n_au_employment_basis_code == 'C'"/>
                        <field name="l10n_au_medicare_reduction" invisible="not l10n_au_tax_free_threshold or not l10n_au_medicare_variation_form or l10n_au_employment_basis_code == 'C'"/>
                        <field name="l10n_au_tax_treatment_code" groups="base.group_no_one"/>
                    </group>
                    <group name="l10n_au_deductions" string="Deductions, offsets and withholding" invisible="company_country_code != 'AU'">
                        <field name="l10n_au_child_support_deduction"/>
                        <field name="l10n_au_child_support_garnishee_amount" widget="percentage"/>
                        <field name="l10n_au_nat_3093_amount"
                            invisible="(l10n_au_tax_treatment_category != 'R' or not l10n_au_tax_free_threshold) and l10n_au_tax_treatment_category != 'S'" />
                        <field name="l10n_au_extra_pay"/>
                        <label for="l10n_au_withholding_variation"/>
                        <div class="d-flex align-items-baseline">
                            <field name="l10n_au_withholding_variation" nolabel="1" class="oe_inline" />
                            <field name="l10n_au_withholding_variation_amount"
                                invisible="l10n_au_withholding_variation == 'none'" nolabel="1"
                                widget="float" class="oe_inline o_input_6ch" />
                            <span invisible="l10n_au_withholding_variation == 'none'" class="oe_grey p-2"> %</span>
                        </div>
                        <field name="l10n_au_additional_withholding_amount" />
                    </group>
                </group>
            </page>
            <xpath expr="//notebook" position="inside">
                <page name="super_accounts" string="Super Accounts" invisible="company_country_code != 'AU'" groups="hr.group_hr_user">
                    <field name="l10n_au_super_account_ids">
                        <list>
                            <field name="fund_id"/>
                            <field name="fund_type"/>
                            <field name="date_from"/>
                            <field name="account_active" widget="boolean_toggle"/>
                            <field name="proportion" widget="percentage" optional="hide"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
