# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_enterprise
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: pos_enterprise
#: model:ir.model,name:pos_enterprise.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: pos_enterprise
#: model_terms:ir.ui.view,arch_db:pos_enterprise.res_config_settings_view_form
msgid "Food Delivery Connector"
msgstr ""

#. module: pos_enterprise
#: model:ir.model.fields,field_description:pos_enterprise.field_pos_config__module_pos_iot
msgid "IoT Box"
msgstr "IoT Box"

#. module: pos_enterprise
#: model:ir.model.fields,field_description:pos_enterprise.field_pos_config__module_pos_urban_piper
msgid "Is an Urbanpiper"
msgstr ""

#. module: pos_enterprise
#: model:ir.model.fields,help:pos_enterprise.field_res_config_settings__pos_module_pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_enterprise.res_config_settings_view_form
msgid "Manage your online orders with Urban Piper."
msgstr ""

#. module: pos_enterprise
#: model:ir.model,name:pos_enterprise.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavení prodejního místa"

#. module: pos_enterprise
#: model_terms:ir.ui.view,arch_db:pos_enterprise.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Uložte tuto stránku a vraťte se pro nastavení funkcionality."

#. module: pos_enterprise
#: model:ir.model.fields,field_description:pos_enterprise.field_res_config_settings__pos_module_pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_enterprise.res_config_settings_view_form
msgid "Urban Piper"
msgstr ""
