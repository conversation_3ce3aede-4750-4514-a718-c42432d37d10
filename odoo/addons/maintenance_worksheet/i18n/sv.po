# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance_worksheet
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON>, 2024
# <PERSON>se L, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Ch<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: maintenance_worksheet
#: model:ir.actions.report,print_report_name:maintenance_worksheet.maintenance_request_report
msgid ""
"'Maintenance Request Worksheet - %s - %s' % (object.name, "
"object.user_id.name)"
msgstr ""

#. module: maintenance_worksheet
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.maintenance_request_view_form_inherit_worksheet
msgid "<span class=\"o_stat_text\">Worksheet</span>"
msgstr ""

#. module: maintenance_worksheet
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.maintenance_request_view_form_inherit_worksheet
msgid ""
"<span class=\"o_stat_text\">Worksheet</span>\n"
"                            <span class=\"o_stat_text\">Complete</span>"
msgstr ""

#. module: maintenance_worksheet
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.report_custom_x_maintenance_request_worksheet_template_1
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_7
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.report_custom_x_maintenance_request_worksheet_template_1
msgid "Comments"
msgstr "Kommentarer"

#. module: maintenance_worksheet
#: model:ir.model.fields,help:maintenance_worksheet.field_maintenance_request__worksheet_template_id
msgid ""
"Create templates for each type of request you have and customize their "
"content with your own custom fields."
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_2
msgid "Created by"
msgstr "Skapad av"

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_1
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "Skapad den"

#. module: maintenance_worksheet
#: model_terms:ir.actions.act_window,help:maintenance_worksheet.maintenance_worksheet_action_settings
msgid "Customize worksheet templates for each type of request.<br>"
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model,name:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_3
msgid "Display Name"
msgstr "Visningsnamn"

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_4
msgid "ID"
msgstr "ID"

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_6
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_5
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: maintenance_worksheet
#: model:ir.model,name:maintenance_worksheet.model_maintenance_request
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_8
msgid "Maintenance Request"
msgstr "Underhållsbegäran"

#. module: maintenance_worksheet
#: model:ir.model,name:maintenance_worksheet.model_report_maintenance_worksheet_maintenance_worksheet
msgid "Maintenance Request Worksheet Custom Report"
msgstr ""

#. module: maintenance_worksheet
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.worksheet_page
msgid "Maintenance Request:"
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_model_fields_9
msgid "Name"
msgstr "Namn"

#. module: maintenance_worksheet
#: model_terms:ir.actions.act_window,help:maintenance_worksheet.maintenance_worksheet_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr ""

#. module: maintenance_worksheet
#. odoo-python
#: code:addons/maintenance_worksheet/models/maintenance_request.py:0
msgid "Please select a Worksheet Template."
msgstr ""

#. module: maintenance_worksheet
#: model_terms:ir.ui.view,arch_db:maintenance_worksheet.worksheet_page
msgid "Worksheet"
msgstr "Arbetsblad"

#. module: maintenance_worksheet
#: model:ir.model.fields,field_description:maintenance_worksheet.field_maintenance_request__worksheet_count
msgid "Worksheet Count"
msgstr "Arbetsblad Räkna"

#. module: maintenance_worksheet
#: model:ir.actions.report,name:maintenance_worksheet.maintenance_request_report
msgid "Worksheet Report"
msgstr ""

#. module: maintenance_worksheet
#: model:ir.model,name:maintenance_worksheet.model_worksheet_template
#: model:ir.model.fields,field_description:maintenance_worksheet.field_maintenance_request__worksheet_template_id
msgid "Worksheet Template"
msgstr "Mall kalkylblad"

#. module: maintenance_worksheet
#: model:ir.actions.act_window,name:maintenance_worksheet.action_maintenance_worksheets
#: model:ir.actions.act_window,name:maintenance_worksheet.maintenance_worksheet_action_settings
#: model:ir.ui.menu,name:maintenance_worksheet.menu_maintenance_configuration_worksheet_templates
msgid "Worksheet Templates"
msgstr ""

#. module: maintenance_worksheet
#: model:ir.actions.act_window,name:maintenance_worksheet.x_maintenance_request_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Arbetsblad"
