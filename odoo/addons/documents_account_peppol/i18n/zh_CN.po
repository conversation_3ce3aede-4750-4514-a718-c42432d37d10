# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account_peppol
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: 何彬 <<EMAIL>>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "科目EDI代理用户"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: documents_account_peppol
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_company__documents_account_peppol_tag_ids
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_config_settings__documents_account_peppol_tag_ids
#: model_terms:ir.ui.view,arch_db:documents_account_peppol.res_config_settings_view_form_inherit_documents_account_peppol
msgid "Document Tags"
msgstr "文件标签"

#. module: documents_account_peppol
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_company__documents_account_peppol_folder_id
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_config_settings__documents_account_peppol_folder_id
#: model_terms:ir.ui.view,arch_db:documents_account_peppol.res_config_settings_view_form_inherit_documents_account_peppol
msgid "Document Workspace"
msgstr "文件工作区"

#. module: documents_account_peppol
#. odoo-python
#: code:addons/documents_account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol document (UUID: %(uuid)s) has been received successfully."
msgstr "收取Peppol文件 (UUID: %(uuid)s) 成功。"
