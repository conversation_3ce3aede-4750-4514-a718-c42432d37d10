# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_commission
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:57+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "%s (copy)"
msgstr "%s (kopiuj)"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "<span class=\"o_stat_text\">Commissions</span>"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__achieved
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved
msgid "Achieved"
msgstr "Zdobyto"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__achieved_rate
msgid "Achieved Rate"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__achievement_ids
msgid "Achievement"
msgstr "Osiągnięcie"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_config_settings__group_commission_forecast
msgid "Achievement Forecast"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Achievement Report"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_achievement_action_report
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__achieve
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_report_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Achievements"
msgstr "Osiągnięcia"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__active
msgid "Active"
msgstr "Aktywne"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Active Plans"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add Multiple Salespersons"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_subscription_change_customer_wizard_action
msgid "Add Salespersons"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new Sales Person"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new achievement"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Add a new commission level"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment %s"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_achievement.py:0
msgid "Adjustment: %s"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_achievement
#: model:ir.ui.menu,name:sale_commission.sale_commission_achievement_menu
msgid "Adjustments"
msgstr "Korekty"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All categories"
msgstr "Wszystkie kategorie"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "All products"
msgstr "Wszystkie produkty"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid ""
"Allows you to manually adjust a salesperson's achievements\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
msgid ""
"Allows you to manually set a salesperson's forecast\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid ""
"Allows you to manually set your sales' forecast\n"
"                for a specific period and commission plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__amount
msgid "Amount"
msgstr "Kwota"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_invoiced
msgid "Amount Invoiced"
msgstr "Kwota zafakturowana"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__amount_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__amount_sold
msgid "Amount Sold"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Approve"
msgstr "Akceptuj"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__approved
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Approved"
msgstr "Zaaprobowane"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Based on"
msgstr "Bazując na"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "By Quarter"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__cancel
msgid "Cancelled"
msgstr "Anulowano"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_categ_id
msgid "Category"
msgstr "Kategoria"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__commission
msgid "Commission"
msgstr "Prowizja"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_achievement_view_search
msgid "Commission Achievement"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/report/commission_report.py:0
msgid "Commission Detail: %(name)s"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan
#: model:ir.model.fields,field_description:sale_commission.field_crm_team__commission_plan_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Plan"
msgstr "Plan prowizji"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_achievement
msgid "Commission Plan Achievement"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target
msgid "Commission Plan Target"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_commission
msgid "Commission Plan Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_target_forecast
msgid "Commission Plan Target Forecast"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user
msgid "Commission Plan User"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_plan
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_menu
msgid "Commission Plans"
msgstr "Plany prowizji"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Commission Report"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__commission_plan_users_ids
msgid "Commission plans"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_report_sale
#: model:ir.ui.menu,name:sale_commission.menu_sale_commission
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu
#: model:ir.ui.menu,name:sale_commission.sale_commission_plan_report_menu_report
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Commissions"
msgstr "Prowizje"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__company_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Company"
msgstr "Firma"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Completed"
msgstr "Ukończona"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid ""
"Compute commissions of your sales people based on their achievements and "
"targets"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_plan
msgid "Create a new commission plan"
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_achievement
msgid "Create an adjustment"
msgstr ""

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_forecast
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_forecast
msgid "Create an forecast"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__create_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__currency_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__currency_id
msgid "Currency"
msgstr "Waluta"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__currency_rate
msgid "Currency Rate"
msgstr "Kurs waluty"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "Current Period"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__date
msgid "Date"
msgstr "Data"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_crm_team__commission_plan_ids
msgid "Default commission plan for team members."
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Details"
msgstr "Szczegóły"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__display_name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__done
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Done"
msgstr "Wykonano"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__state__draft
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Draft"
msgstr "Projekt"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Effective Period"
msgstr ""

#. module: sale_commission
#: model:res.groups,name:sale_commission.group_commission_forecast
msgid "Enable Commission Forecast"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "End Date"
msgstr "Data końcowa"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid ""
"Ensure you are assigned to a commission plan and have made sales that "
"qualify for commissions"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_res_users__filtered_commission_plan_users_ids
msgid "Filtered Commission Plan Users"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_forecast
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__amount
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_graph
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_list
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "Forecast"
msgstr "Prognoza"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_from
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_from
msgid "From"
msgstr "Od"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "From must be before To"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Group By"
msgstr "Grupuj wg"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__id
msgid "ID"
msgstr "ID"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Last Quarter"
msgstr "Ostatni kwartał"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_uid
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__write_date
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Last Year"
msgstr "Ostatni rok"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""
"Zespół sprzedaży głównego użytkownika. Używane zwłaszcza do pipeline, lub do"
" ustawienia zespołu sprzedaży w fakturowaniu lub subskrypcji."

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement
msgid "Manual Commission Achievement"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Mark as done"
msgstr "Oznacz jako wykonane"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__month
msgid "Monthly"
msgstr "Miesięcznie"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
msgid "My Achievements"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_report
#: model:ir.ui.menu,name:sale_commission.sale_commission_my_menu
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "My Commissions"
msgstr ""

#. module: sale_commission
#: model:ir.actions.act_window,name:sale_commission.sale_commission_action_my_forecast
msgid "My Forecast"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "My Team"
msgstr "Mój zespół"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__name
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Name"
msgstr "Nazwa"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__note
msgid "Note"
msgstr "Notatka"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__commission_amount
msgid "OTC"
msgstr "Pozagiełdowe instrumenty pochodne (OTC)"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "OTC %"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__commission_amount
msgid "On Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan_target_commission__amount_rate
msgid "On Target Commission rate"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Ongoing"
msgstr "Na bieżąco"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__other_plans
msgid "Other plans"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__payment_date
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Payment Date"
msgstr "Data płatności"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__name
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__target_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_id
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Period"
msgstr "Okres"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__periodicity
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Periodicity"
msgstr "Okresowość"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Periods"
msgstr "Okresy"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__plan_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__plan_id
msgid "Plan"
msgstr "Plan"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__product_id
msgid "Product"
msgstr "Produkt"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_invoiced
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_invoiced
msgid "Quantity Invoiced"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_achievement__type__qty_sold
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan_achievement__type__qty_sold
msgid "Quantity Sold"
msgstr "Ilość sprzedana"

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__quarter
msgid "Quarterly"
msgstr "Kwartalnie"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__rate
msgid "Rate"
msgstr "Kurs"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__rating_ids
msgid "Ratings"
msgstr "Oceny"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_id
msgid "Related"
msgstr "Powiązane"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__related_res_model
msgid "Related Res Model"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "Related commissions"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Reset to draft"
msgstr "Reset do wersji roboczej"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__team_id
msgid "Sale team"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_achievement_report
msgid "Sales Achievement Report"
msgstr ""

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_report
msgid "Sales Commission Report"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Sales People"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__user_id
msgid "Sales Person"
msgstr "Sprzedawca"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_crm_team
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement_report__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__team_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__team
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Sales Team"
msgstr "Zespół sprzedaży"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__user_id
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__user_type__person
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_search
msgid "Salesperson"
msgstr "Sprzedawca"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "See associated achievements"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_achievement_report_view_list
msgid "Source"
msgstr "Źródło"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_list
msgid "Start Date"
msgstr "Data początkowa"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__state
msgid "State"
msgstr "Stan"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_user_wizard
msgid "Submit"
msgstr "Wyślij"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__amount
msgid "Target"
msgstr "Cel"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__target_amount
msgid "Target Amount"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_ids
msgid "Target Commission"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__target_commission_graph
msgid "Target Commission Graph"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Target Frequency"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_commission__target_rate
msgid "Target completion (%)"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__type__target
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "Targets"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid ""
"The plan should have at least one target with an achievement rate of 0%"
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The start date must be before the end date."
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan.py:0
msgid "The team is required in team plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.constraint,message:sale_commission.constraint_sale_commission_plan_user_user_uniq
msgid "The user is already present in the plan"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Month"
msgstr "Bieżący miesiąc"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_target_forecast_view_search
msgid "This Year"
msgstr "W tym roku"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user__date_to
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__date_to
msgid "To"
msgstr "Do"

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Total Target"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Total achieved"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_report_view_list
msgid "Total commission"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__type
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_achievement__type
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Type"
msgstr "Typ"

#. module: sale_commission
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_my_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report
#: model_terms:ir.actions.act_window,help:sale_commission.sale_commission_action_report_sale
msgid "Unfortunately, there are no commissions for you"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_search
msgid "Upcoming"
msgstr "Nadchodzące"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_res_users
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_ids
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__user_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_user_wizard__user_ids
msgid "User"
msgstr "Użytkownik"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_achievement__team_id
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan_target_forecast__team_id
msgid "User Sales Team"
msgstr "Zespół sprzedaży użytkownika"

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__user_type
msgid "User Type"
msgstr "Typ użytkownika"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot end after the plan."
msgstr ""

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_user.py:0
msgid "User period cannot start before the plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: sale_commission
#: model:ir.model.fields,help:sale_commission.field_sale_commission_plan__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: sale_commission
#: model:ir.model,name:sale_commission.model_sale_commission_plan_user_wizard
msgid "Wizard for selecting multiple users"
msgstr ""

#. module: sale_commission
#: model:ir.model.fields.selection,name:sale_commission.selection__sale_commission_plan__periodicity__year
msgid "Yearly"
msgstr "Rocznie"

#. module: sale_commission
#. odoo-python
#: code:addons/sale_commission/model/commission_plan_target_forecast.py:0
msgid "You cannot create a forecast for an user that is not in the plan."
msgstr ""

#. module: sale_commission
#: model:ir.model.fields,field_description:sale_commission.field_sale_commission_report__forecast_id
msgid "fc"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "i.e. Commissions plan 2025"
msgstr ""

#. module: sale_commission
#: model_terms:ir.ui.view,arch_db:sale_commission.sale_commission_plan_view_form
msgid "per"
msgstr "za"
