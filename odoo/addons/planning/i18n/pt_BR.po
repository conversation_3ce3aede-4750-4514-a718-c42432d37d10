# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 20:43+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: planning
#. odoo-python
#: code:addons/planning/models/resource_resource.py:0
msgid "%(resource_name)s (%(role)s)"
msgstr "%(resource_name)s (%(role)s)"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: planning
#: model:ir.actions.report,print_report_name:planning.report_planning_slot
msgid "'Planning'"
msgstr "\"Planejamento\""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid "(%s days span)"
msgstr "(%s dias de intervalo)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid ".&amp;nbsp;"
msgstr ".&amp;nbsp;"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 5:00 PM"
msgstr "01/01/2023 17h00"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 9:00 AM"
msgstr "01/01/2023 9h00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "04:00"
msgstr "04h00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "08:00"
msgstr "08h00"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Onsite Interview"
msgstr "1 Entrevista presencial"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Phone Call"
msgstr "1 Ligação"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "100"
msgstr "100"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "12:00"
msgstr "12h00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "13"
msgstr "13"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "14"
msgstr "14"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "15"
msgstr "15"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "16:00"
msgstr "16h00"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "2 open days"
msgstr "2 dias abertos"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "4 Days after Interview"
msgstr "4 dias após a entrevista"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "8 hours"
msgstr "8 horas"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<b class=\"o_gantt_title d-flex align-items-center justify-content-center "
"bg-100 position-sticky start-0 p-2 border-end\"> Schedule</b>"
msgstr ""
"<b class=\"o_gantt_title d-flex align-items-center justify-content-center "
"bg-100 position-sticky start-0 p-2 border-end\"> Agendar</b>"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr ""
"<b class=\"tip_title\">Dica: Registre seu planejamento mais rapidamente</b>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Allocated Time:</b>"
msgstr "<b>Tempo alocado:</b>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"<b>Drag & drop</b> your shift to reschedule it. <i>Tip: hit CTRL (or Cmd) to"
" duplicate it instead.</i> <b>Adjust the size</b> of the shift to modify its"
" period."
msgstr ""
"<b>Arraste e solte</b> seu turno para reprogramá-lo. <i>Dica: Em vez disso, "
"pressione CTRL para duplicá-lo.</i> <b>Ajuste o tamanho</b> do turno para "
"modificar o período."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Note:</b>"
msgstr "<b>Nota:</b>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "<b>Publish & send</b> your employee's planning."
msgstr "<b>Publicar e enviar</b> o planejamento do seu funcionário."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Role:</b>"
msgstr "<b>Função:</b>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_shift_switch_email
msgid ""
"<div>\n"
"                    <p t-if=\"ctx.get('old_assignee_name')\">Dear <t t-out=\"ctx['old_assignee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p>\n"
"                        The following shift that you requested to switch has been re-assigned\n"
"                        <t t-if=\"ctx.get('new_assignee_name')\"> to <t t-out=\"ctx['new_assignee_name']\">Marc Demo</t></t>.\n"
"                    </p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p t-if=\"ctx.get('old_assignee_name')\">Prezado(a) <t t-out=\"ctx['old_assignee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Olá,</p>\n"
"                    <br/>\n"
"                    <p>\n"
"                        O seguinte turno que você solicitou para trocar foi reatribuído\n"
"                        <t t-if=\"ctx.get('new_assignee_name')\"> a <t t-out=\"ctx['new_assignee_name']\">Marc Demo</t></t>.\n"
"                    </p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Data</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    31/05/2021, 8h00 ⟶ 31/05/2021, 16h00\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Função</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Projeto</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Confraternização</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Item do pedido de venda</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Café</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Nota</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/>\n"
"                </div>\n"
"            "

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/>\n"
"                        You have successfully requested to switch your shift. You will be notified when another employee takes over your shift."
msgstr ""
"<i class=\"fa fa-check-circle\"/>\n"
"                        A sua solicitação de troca de turno foi realizada com sucesso. Você será notificado quando outro funcionário assumir o seu turno."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> Request to switch shift cancelled "
"successfully."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Solicitação de troca de turno cancelada "
"com sucesso."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid "<i class=\"fa fa-check-circle\"/> This shift is no longer assigned to you."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Esse turno não está mais atribuído a você."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> You were successfully assigned this open "
"shift."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Esse turno aberto foi atribuído a você com"
" sucesso."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> This shift is already assigned to "
"another employee."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> Esse turno já está atribuído a outro"
" funcionário."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> You can no longer unassign yourself "
"from this shift."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> Você não pode mais retirar a sua "
"atribuição desse turno."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"
msgstr "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"mt-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"mt-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Bartender</span>"
msgstr ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Bartender</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Waiter</span>"
msgstr ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Garçom</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"ms-0 me-4\">Edit</span>"
msgstr "<span class=\"ms-0 me-4\">Editar</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">04h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">08:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">08h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">12h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 "
"PM</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">13h00 - 17h00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 "
"PM</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">08h00 - 12h00</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"
msgstr ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
msgid "<span class=\"o_stat_text\">Planning</span>"
msgstr "<span class=\"o_stat_text\">Planejamento</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">dias para receber uma oferta</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Processo</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Tempo para responder</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1 text-start\">Open Shifts</span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1 text-start\">Turnos "
"abertos</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Doris Cole </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Doris Cole </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Eli Lambert </span> "
"<span class=\"text-muted text-truncate\">(Waiter)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Eli Lambert </span> "
"<span class=\"text-muted text-truncate\">(Garçom)</span> </span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Jeffrey Kelly </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Jeffrey Kelly </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"

#. module: planning
#: model_terms:web_tour.tour,rainbow_man_message:planning.planning_tour
msgid ""
"<span><b>Congratulations!</b> You are now a master of planning.\n"
"        </span>"
msgstr ""
"<span><b>Parabéns!</b> Agora você é mestre de planejamentos.\n"
"</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>Allow employees to:</span>"
msgstr "<span>Permitir que os funcionários:</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid ""
"<span>The employee assigned would like to switch shifts with someone "
"else.</span>"
msgstr ""
"<span>O funcionário atribuído gostaria de trocar de turno com outra "
"pessoa.</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months ahead</span>"
msgstr "<span>próximos meses</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>Tempo alocado — </strong>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<style>\n"
"                    .planning_mail_template_button {\n"
"                        padding: 5px 10px;\n"
"                        text-decoration: none;\n"
"                        border: 1px;\n"
"                        border-radius: 3px;\n"
"                        text-align: center;\n"
"                        color: #374151;\n"
"                        background-color: #E7E9ED;\n"
"                        border: solid #E7E9ED;\n"
"                        flex: 1 1 40%;\n"
"                    }\n"
"                    .top_button {\n"
"                        color: #FFFFFF;\n"
"                        background-color: #714B67;\n"
"                        border: solid #714B67;\n"
"                        flex: 1 1 60%;\n"
"                    }\n"
"                    .button_box {\n"
"                        display: flex;\n"
"                        flex-wrap: wrap;\n"
"                        gap: 10px;\n"
"                        margin: 20px 15px 0px 15px;\n"
"                    }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee_name')\">Dear <t t-out=\"ctx['employee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">We have a new shift opening:</p>\n"
"                    <p t-else=\"\">You have been assigned the following shift:</p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <div class=\"button_box\">\n"
"                        <a t-if=\"ctx.get('available_link')\" t-att-href=\"ctx['available_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Assign me this shift</a>\n"
"                        <a t-if=\"ctx.get('unavailable_link')\" t-att-href=\"ctx['unavailable_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">I am unavailable</a>\n"
"                        <a t-if=\"ctx.get('google_url')\" t-att-href=\"ctx['google_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to Google Calendar</a>\n"
"                        <a t-if=\"ctx.get('iCal_url')\" t-att-href=\"ctx['iCal_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to iCal/Outlook</a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">If you are interested and available, please assign yourself this open shift.</p>\n"
"                    <p t-else=\"\">In case the current shift doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""
"<style>\n"
"                    .planning_mail_template_button {\n"
"                        padding: 5px 10px;\n"
"                        text-decoration: none;\n"
"                        border: 1px;\n"
"                        border-radius: 3px;\n"
"                        text-align: center;\n"
"                        color: #374151;\n"
"                        background-color: #E7E9ED;\n"
"                        border: solid #E7E9ED;\n"
"                        flex: 1 1 40%;\n"
"                    }\n"
"                    .top_button {\n"
"                        color: #FFFFFF;\n"
"                        background-color: #714B67;\n"
"                        border: solid #714B67;\n"
"                        flex: 1 1 60%;\n"
"                    }\n"
"                    .button_box {\n"
"                        display: flex;\n"
"                        flex-wrap: wrap;\n"
"                        gap: 10px;\n"
"                        margin: 20px 15px 0px 15px;\n"
"                    }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee_name')\">Prezada <t t-out=\"ctx['employee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Olá,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">Temos vaga para um novo turno:</p>\n"
"                    <p t-else=\"\">Você foi atribuído ao seguinte turno:</p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Data</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    31/05/2021, 8h ⟶ 31/05/2021, 16h00\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Função</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Projeto</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Confraternização</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Item do pedido de venda</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Café</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Nota</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <div class=\"button_box\">\n"
"                        <a t-if=\"ctx.get('available_link')\" t-att-href=\"ctx['available_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Atribuir este turno a mim</a>\n"
"                        <a t-if=\"ctx.get('unavailable_link')\" t-att-href=\"ctx['unavailable_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Nãp estou disponível</a>\n"
"                        <a t-if=\"ctx.get('google_url')\" t-att-href=\"ctx['google_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Adicionar ao Google Calendar</a>\n"
"                        <a t-if=\"ctx.get('iCal_url')\" t-att-href=\"ctx['iCal_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Adicionar ao iCal/Outlook</a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">Se tiver interesse e disponibilidade, atribua este turno aberto a você mesmo.</p>\n"
"                    <p t-else=\"\">Caso o turno atual não seja adequado para você, recomendamos que entre em contato com seus colegas e solicite uma troca de turnos. Eles podem estar interessados em trocar de turno com você.</p>\n"
"                </div>\n"
"            </body>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<style>\n"
"                .planning_mail_template_button {\n"
"                    padding: 5px 10px;\n"
"                    text-decoration: none;\n"
"                    border: 1px;\n"
"                    border-radius: 3px;\n"
"                    display: inline-block; \n"
"                    width: 190px;\n"
"                    text-align: center;\n"
"                }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee')\">Dear <t t-out=\"ctx['employee'].name or ''\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\">\n"
"                        Your upcoming shifts from <t t-out=\"format_date(ctx['start_datetime'])\">05-05-2021</t>\n"
"                        to <t t-out=\"format_date(ctx['end_datetime'])\">05-11-2021</t> have been published.\n"
"                    </p>\n"
"                    <div style=\"display: flex; margin: 15px;\">\n"
"                        <div t-if=\"ctx.get('planning_url')\" style=\"margin-right: 15px;\">\n"
"                            <a t-att-href=\"ctx['planning_url']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #FFFFFF; background-color: #875A7B; border: solid #875A7B;\">View Your Planning</a>\n"
"                        </div>\n"
"                        <div t-if=\"ctx.get('planning_url_ics')\">\n"
"                            <a t-att-href=\"ctx['planning_url_ics']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #374151; background-color: #E7E9ED; border: solid #E7E9ED;\">Add to Calendar</a>\n"
"                        </div>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('slot_unassigned') or (object.allow_self_unassign and object.self_unassign_days_before)\">\n"
"                        <t t-if=\"ctx.get('slot_unassigned')\">\n"
"                            We would also like to remind you that there are some open shifts available, and if you are interested and available, please assign yourself to those shifts.\n"
"                        </t>\n"
"                        <t t-if=\"object.allow_self_unassign and object.self_unassign_days_before\">\n"
"                            If you are unable to work a shift that has been assigned to you, please unassign yourself within <t t-out=\"object.self_unassign_days_before or ''\">5</t> day(s) before the start of the shift.\n"
"                        </t>\n"
"                    </p>\n"
"                    <p t-if=\"ctx.get('message')\" t-out=\"ctx['message']\"/>\n"
"                    <p t-if=\"object.allow_self_unassign\">In case your current schedule doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""
"<style>\n"
"                .planning_mail_template_button {\n"
"                    padding: 5px 10px;\n"
"                    text-decoration: none;\n"
"                    border: 1px;\n"
"                    border-radius: 3px;\n"
"                    display: inline-block; \n"
"                    width: 190px;\n"
"                    text-align: center;\n"
"                }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee')\">Prezada <t t-out=\"ctx['employee'].name or ''\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Olá,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\">\n"
"                        Seus próximos turnos de <t t-out=\"format_date(ctx['start_datetime'])\">05-05-2021</t>\n"
"                        até <t t-out=\"format_date(ctx['end_datetime'])\">11-05-2021</t> foram publicados.\n"
"                    </p>\n"
"                    <div style=\"display: flex; margin: 15px;\">\n"
"                        <div t-if=\"ctx.get('planning_url')\" style=\"margin-right: 15px;\">\n"
"                            <a t-att-href=\"ctx['planning_url']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #FFFFFF; background-color: #875A7B; border: solid #875A7B;\">Visualizar seu planejamento</a>\n"
"                        </div>\n"
"                        <div t-if=\"ctx.get('planning_url_ics')\">\n"
"                            <a t-att-href=\"ctx['planning_url_ics']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #374151; background-color: #E7E9ED; border: solid #E7E9ED;\">Adicionar ao calendário</a>\n"
"                        </div>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('slot_unassigned') or (object.allow_self_unassign and object.self_unassign_days_before)\">\n"
"                        <t t-if=\"ctx.get('slot_unassigned')\">\n"
"                            Gostaríamos também de lembrá-lo de que há alguns turnos disponíveis e, se você estiver interessado e disponível, pode solicitar esses turnos.\n"
"                        </t>\n"
"                        <t t-if=\"object.allow_self_unassign and object.self_unassign_days_before\">\n"
"                            Se você não puder trabalhar em um turno que lhe foi atribuído, cancele sua atribuição dentro de <t t-out=\"object.self_unassign_days_before or ''\">5</t> dia(s) antes do início do turno.\n"
"                        </t>\n"
"                    </p>\n"
"                    <p t-if=\"ctx.get('message')\" t-out=\"ctx['message']\"/>\n"
"                    <p t-if=\"object.allow_self_unassign\">Caso o horário atual não seja adequado para você, recomendamos que entre em contato com seus colegas e solicite uma troca de turnos. Eles podem estar interessados em trocar de turno com você.</p>\n"
"                </div>\n"
"            </body>"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr ""
"Recorrências que se repetem até uma determinada data deve ter um limite "
"definido"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "A shift must be in the same company as its recurrence."
msgstr "Um turno deve estar na mesma empresa que a sua recorrência."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__active
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__active
msgid "Active"
msgstr "Ativo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.js:0
msgid "Add Shift"
msgstr "Adicionar turno"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr "Mensagem adicional exibida no e-mail enviado aos funcionários"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Additional message included in the email sent to your employees"
msgstr "Mensagem adicional incluída no e-mail enviado aos seus funcionários"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional note sent to the employee"
msgstr "Nota adicional enviada ao funcionário"

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "Administrador"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"All open shifts have already been assigned, or there are no resources "
"available to take them at this time."
msgstr ""
"Todos os turnos abertos já foram atribuídos ou não há recursos disponíveis "
"para assumi-los neste momento."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__all
msgid "All shifts"
msgstr "Todos os turnos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr ""
"Todos os turnos subsequentes serão excluídos. Tem certeza de que quer "
"continuar?"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Hours"
msgstr "Horas alocadas"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Percentage"
msgstr "Porcentagem alocada"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_hours
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Allocated Time"
msgstr "Tempo alocado"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time %"
msgstr "Tempo alocado %"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_percentage
msgid "Allocated Time (%)"
msgstr "Tempo alocado (%)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Allocated Time:"
msgstr "Tempo alocado:"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "Allocated hours and allocated time percentage cannot be negative."
msgstr ""
"As horas alocadas e a porcentagem de tempo alocada não podem ser negativas."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr "Tipo de alocação"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid ""
"Analyze the allocation of your resources across roles, projects, and sales "
"orders, and estimate future needs."
msgstr ""
"Analise a alocação dos seus recursos entre funções, projetos e ordens de "
"vendas e faça previsões de suas necessidades futuras."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/resource_form/resource_form_controller.js:0
#: code:addons/planning/static/src/views/resource_list/resource_list_controller.js:0
msgid ""
"Archiving this resource will transform all of its future shifts into open "
"shifts. Are you sure you want to continue?"
msgstr ""
"Arquivar este recurso transformará todos os seus turnos futuros em turnos "
"abertos. Tem certeza de que quer continuar?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
msgid "Are you sure you want to delete this shift?"
msgstr "Tem certeza de que quer excluir esse turno?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow"
msgstr "Flecha"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow icon"
msgstr "Ícone de flecha"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Ask To Switch"
msgstr "Pedir para trocar"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Ask to Switch"
msgstr "Pedir para trocar"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Assign a <b>resource</b>, or leave it open for the moment. <i>Tip: Create "
"open shifts for the roles you will be needing to complete a mission. Then, "
"assign those open shifts to the resources that are available.</i>"
msgstr ""
"Atribua um <b>recurso</b> ou deixe aberto por enquanto. <i>Dica: Crie turnos"
" abertos para as funções que você precisará para cumprir uma missão. Então, "
"atribua esses turnos abertos aos recursos disponíveis</i>."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Assignee"
msgstr "Responsável"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Auto Plan"
msgstr "Planejamento automático"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Automatically plan open shifts and sales orders"
msgstr "Planeje turnos abertos e ordens de vendas automaticamente"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_filter_panel/planning_calendar_filter_panel.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Bartender"
msgstr "Bartender"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee_base
msgid "Basic Employee"
msgstr "Funcionário básico"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_resource
msgid "By Resource"
msgstr "Por recurso"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr "Por função"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "CANCEL SWITCH"
msgstr "CANCELAR TROCA"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Cancel Switch"
msgstr "Cancelar troca"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Chat"
msgstr "Chat"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
msgid "Color"
msgstr "Cor"

#. module: planning
#: model:planning.role,name:planning.planning_role_cm
msgid "Community Manager"
msgstr "Gerente da comunidade"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__company_id
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Company"
msgstr "Empresa"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__company_id
msgid ""
"Company linked to the material resource. Leave empty for the resource to be "
"available in every company."
msgstr ""
"Empresa vinculada ao recurso material. Deixe em branco para que o recurso "
"fique disponível para todas as empresas."

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "Configuração"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Confirm"
msgstr "Confirmar"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Copy previous"
msgstr "Copiar anterior"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Copy previous week"
msgstr "Copiar semana anterior"

#. module: planning
#: model:planning.role,name:planning.planning_role_crane
msgid "Crane"
msgstr "Guindaste"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
msgid "Created on"
msgstr "Criado em"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "Data"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_end
msgid "Date End"
msgstr "Data de término"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_start
msgid "Date Start"
msgstr "Data de início"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__day
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__day
msgid "Days"
msgstr "Dias"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Days before shift for unassignment"
msgstr "Dias anteriores ao turno para retirar atribuição"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Deadline"
msgstr "Prazo final"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Deadline in days for shift unassignment"
msgstr "Prazo final em dias para retirar atribuição do turno"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
#: model:ir.model.fields,field_description:planning.field_resource_resource__default_role_id
msgid "Default Role"
msgstr "Função padrão"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
msgid "Default Roles"
msgstr "Funções padrão"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid ""
"Define a work email address for the following employees so they will receive"
" the planning."
msgstr ""
"Defina um endereço de e-mail de trabalho para os seguintes funcionários para"
" que recebam o planejamento."

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__role_id
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you need to complete a mission. Then, "
"assign those open shifts to the resources that are available."
msgstr ""
"Defina as funções realizadas por seus recursos (Ex: chef, bartender, garçom "
"etc.). Crie turnos abertos para as funções necessárias para cumprir uma "
"missão. Então, atribua esses turnos abertos aos recursos disponíveis."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you will be needing to complete a mission. "
"Then, assign those open shifts to the resources that are available."
msgstr ""
"Defina as funções realizadas por seus recursos (Ex: chef, bartender, garçom "
"etc.). Crie turnos abertos para as funções que serão necessárias para "
"cumprir uma missão. Então, atribua esses turnos abertos aos recursos "
"disponíveis."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Delete"
msgstr "Excluir"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.js:0
msgid "Delete Recurring Shift"
msgstr "Excluir turno recorrente"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__department_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Department"
msgstr "Departamento"

#. module: planning
#: model:ir.model,name:planning.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Assistente de desligamento"

#. module: planning
#: model:planning.role,name:planning.planning_role_developer
msgid "Developer"
msgstr "Desenvolvedor"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Discard"
msgstr "Cancelar"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "Distribute your material resources across projects and sales orders."
msgstr "Distribua seus recursos materiais entre projetos e ordens de vendas."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Doris Cole (Bartender)"
msgstr "Doris Cole (Bartender)"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__draft
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Draft"
msgstr "Provisório"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. Press CTRL (or Cmd on Mac) while dragging a shift to duplicate "
"it."
msgstr ""
"Arraste um turno até outro dia para reagendá-lo ou até outra linha para "
"reatribui-lo. Pressione CTRL (ou Cmd no Mac) ao arrastar um turno para "
"duplicá-lo."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__duration
msgid "Duration"
msgstr "Duração"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration_days
msgid "Duration Days"
msgstr "Dias de duração"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit"
msgstr "Editar"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Edit Recurrent Shift"
msgstr "Editar turno recorrente"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Eli Lambert (Waiter)"
msgstr "Eli Lambert (Garçom)"

#. module: planning
#: model:mail.template,description:planning.email_template_shift_switch_email
msgid ""
"Email sent automatically when an employee self-assigns to the unwanted shift"
" of another employee, notifying the person who requested to switch that the "
"shift has been taken"
msgstr ""
"Email sent automatically when an employee self-assigns to the unwanted shift"
" of another employee, notifying the person who requested to switch that the "
"shift has been taken"

#. module: planning
#: model:mail.template,description:planning.email_template_slot_single
msgid "Email sent automatically when publishing a shift"
msgstr "E-mail enviado automaticamente ao publicar um turno"

#. module: planning
#: model:mail.template,description:planning.email_template_planning_planning
msgid ""
"Email sent automatically when publishing the schedule of your employees"
msgstr ""
"E-mail enviado automaticamente ao publicar a agenda dos seus funcionários"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__employee_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
msgid "Employee"
msgstr "Funcionário"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Employee Name"
msgstr "Nome do funcionário"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_employee_unavailabilities
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_employee_unavailabilities
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Employee Unavailabilities"
msgstr "Indisponibilidade de funcionários"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_employee
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Employees"
msgstr "Funcionários"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Employees with no Work Email"
msgstr "Funcionários sem e-mail de trabalho"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "End"
msgstr "Fim"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "End Date"
msgstr "Data de término"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__end_time
msgid "End Hour"
msgstr "Horário de término"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr "Erro: os tokens de funcionário devem ser exclusivos"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Every %(repeat_interval)s week(s) until %(repeat_until)s"
msgstr "A cada %(repeat_interval)s semana(s) até %(repeat_until)s"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr "Mensagem adicional"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
msgid ""
"Find here your planning. Assign yourself open shifts that match your roles, "
"or indicate your unavailability."
msgstr ""
"Consulte seu planejamento aqui. Atribua a si mesmo turnos abertos que "
"correspondam às suas funções ou indique sua indisponibilidade."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "First you have to specify the date of the invitation."
msgstr "Especifique primeiro a data do convite."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Fixed Hours"
msgstr "Horas fixas"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Flexible Hours"
msgstr "Horas flexíveis"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "Previsão"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "Para sempre"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Forever, every %s week(s)"
msgstr "Para sempre, a cada %s semana(s)"

#. module: planning
#: model:planning.role,name:planning.planning_furniture_assembler
msgid "Furniture Assembler"
msgstr "Montador de móveis"

#. module: planning
#: model:planning.role,name:planning.planning_furniture_tool
msgid "Furniture Tools"
msgstr "Ferramentas para móveis"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Generate shifts"
msgstr "Gerar turnos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr "Dar profundidade a"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Group By"
msgstr "Agrupar por"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_base__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_public__has_slots
msgid "Has Slots"
msgstr "Tem faixas de horário"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__request_to_switch
msgid "Has there been a request to switch on this shift slot?"
msgstr "Houve alguma solicitação de troca nessa faixa de turnos?"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__name
msgid "Hours"
msgstr "Horas"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__user
msgid "Human"
msgstr "Humano"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Am Unavailable"
msgstr "Estou indisponível"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Take It"
msgstr "Eu assumo"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr "Estou indisponível"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
msgid "ID"
msgstr "ID"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr ""
"Se marcado, significa que o conteúdo do turno foi alterado desde sua última "
"publicação."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"If you are happy with your planning, you can now <b>send</b> it to your "
"employees."
msgstr ""
"Se está satisfeito com seu planejamento, você pode <b>enviá-lo</b> para seus"
" funcionários agora."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr "Incluir turnos abertos"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr "Inclui turnos abertos"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Jeffrey Kelly (Bartender)"
msgstr "Jeffrey Kelly (Bartender)"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__job_title
#: model:ir.model.fields,field_description:planning.field_planning_slot__job_title
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Job Title"
msgstr "Função"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Legend"
msgstr "Legenda"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__allow_self_unassign
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr "Permitir que funcionários retirem suas atribuições"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Let employees switch shifts with colleagues or unassign themselves when "
"unavailable"
msgstr ""
"Permitir que os funcionários troquem de turnos com os colegas ou retirem "
"suas atribuições quando não estiverem disponíveis"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's check out the Gantt view for cool features. Get ready to <b>share your"
" schedule</b> and easily plan your shifts with just one click by <em>copying"
" the previous week's schedule</em>."
msgstr ""
"Vamos dar uma olhada na visualização Gantt para conhecer seus recursos "
"interessantes. Prepare-se para <b>compartilhar sua agenda</b> e planejar "
"turnos com apenas um clique, <em>copiando o cronograma da semana "
"anterior</em>."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's create your first <b>shift</b>. <i>Tip: use the (+) shortcut available"
" on each cell of the Gantt view to save time.</i>"
msgstr ""
"Vamos criar seu primeiro <b>turno</b>. <i>Dica: Use o atalho (+) disponível "
"em cada célula da visão de Gantt para ganhar tempo.</i>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's schedule a <b>shift</b> for this time range."
msgstr "Vamos programar um <b>turno</b> nesse intervalo de tempo."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's start managing your employees' schedule!"
msgstr "Vamos gerenciar a agenda dos seus funcionários."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "List"
msgstr "Lista"

#. module: planning
#: model:hr.job,name:planning.job_maintenance_technician
#: model:planning.role,name:planning.planning_maintenance_technician
msgid "Maintenance Technician"
msgstr "Técnico de manutenção"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Make encoding shifts easy with templates."
msgstr "Facilite a codificação de turnos usando modelos."

#. module: planning
#: model:planning.role,name:planning.planning_role_management
msgid "Management"
msgstr "Gestão"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__manager_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Manager"
msgstr "Gerente"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__material
msgid "Material"
msgstr "Material"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_resources
#: model:ir.ui.menu,name:planning.planning_menu_settings_resource
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Materials"
msgstr "Materiais"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "May 2024"
msgstr "Maio de 2024"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr "Modificado desde a última publicação"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Month"
msgstr "Mês"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__month
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__month
msgid "Months"
msgstr "Meses"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Department"
msgstr "Meu departamento"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr "Meu planejamento"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Roles"
msgstr "Minhas funções"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Shifts"
msgstr "Meus cargos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Team"
msgstr "Minha equipe"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "Nome"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Event"
msgstr "Novo evento"

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Shift"
msgstr "Novo turno"

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
msgid "New_Shift"
msgstr "New_Shift"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Next Week"
msgstr "Próxima Semana"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "No Email Address for Some Employees"
msgstr "Alguns funcionários sem endereço de e-mail"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_number
msgid "No Of Repetitions of the plannings"
msgstr "Número de repetições dos planejamentos"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "Sem dados ainda!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "No material resources found. Let's create one!"
msgstr "Nenhum recurso material encontrado. Vamos criá-los!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found. Let's create one!"
msgstr "Nenhuma função encontrada. Vamos criá-las!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found. Let's create one!"
msgstr "Nenhum modelo de turno encontrado. Vamos criá-los!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found. Let's create one!"
msgstr "Nenhum turno encontrado. Vamos criá-los!"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__name
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Note"
msgstr "Nota"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Note:"
msgstr "Nota:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Now that this week is ready, let's get started on <b>next week's "
"schedule</b>."
msgstr ""
"Agora que esta semana está pronta, vamos começar o <b>cronograma da próxima "
"semana</b>."

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__x_times
msgid "Number of Occurrences"
msgstr "Número de ocorrências"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__x_times
msgid "Number of Repetitions"
msgstr "Número de repetições"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Open Shift"
msgstr "Turno aberto"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_model.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_model.js:0
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Open Shifts"
msgstr "Turnos abertos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "Open Shifts Available"
msgstr "Turnos abertos disponíveis"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts assigned"
msgstr "Turnos abertos atribuídos"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts unscheduled"
msgstr "Turnos abertos não programados"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "Open: %s"
msgstr "Abrir: %s"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"Operation not supported, you should always compare overlap_slot_count to 0 "
"value with = or > operator."
msgstr ""
"Operação não suportada. Você sempre deve comparar overlap_slot_count ao "
"valor 0 com o operador = ou >."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "Período"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr ""
"Planeje a alocação de recursos entre projetos e estime prazos com mais "
"precisão"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>."
msgstr ""
"Planeje seus turnos com um clique <b>copiando a programação da semana "
"anterior</b>."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>. Open the menu to access this option."
msgstr ""
"Planeje seus turnos com um clique <b>copiando a programação da semana "
"anterior</b>. Abra o menu para acessar essa opção."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Planned Hours"
msgstr "Horas planejadas"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model:ir.actions.report,name:planning.report_planning_slot
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Planning"
msgstr "Planejamento"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
msgid "Planning Analysis"
msgstr "Análise do planejamento"

#. module: planning
#: model:ir.model,name:planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Relatório de análise do planejamento"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Planning Meeting"
msgstr "Reunião de planejamento"

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr "Recorrência do planejamento"

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr "Função de planejamento"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr "Lista de funções do planejamento"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr "Turno do planejamento"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__slot_id
msgid "Planning Slot"
msgstr "Faixa de horário do planejamento"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__slot_properties_definition
msgid "Planning Slot Properties"
msgstr "Propriedades de faixas de horário de planejamento"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr "Planejamento:"

#. module: planning
#: model:mail.template,name:planning.email_template_planning_planning
msgid "Planning: New Schedule"
msgstr "Planejamento: Nova agenda"

#. module: planning
#: model:mail.template,name:planning.email_template_slot_single
msgid "Planning: New Shift"
msgstr "Planejamento: Novo turno"

#. module: planning
#: model:mail.template,name:planning.email_template_shift_switch_email
msgid "Planning: Shift Re-assigned"
msgstr "Planejamento: Turno reatribuído"

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
msgid "Planning: generate next recurring shifts"
msgstr "Planejamento: Gerar próximos turnos recorrentes"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new open shift available on"
msgstr "Planejamento: Novo turno aberto disponível em"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new shift on"
msgstr "Planejamento: Novo turno em"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "Planos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_email
msgid ""
"Please add a work email for the following employee so that they can receive "
"their planning:"
msgstr ""
"Adicione um e-mail profissional para o seguinte funcionário para que ele "
"possa receber seu planejamento:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Prepare for the ultimate multi-tasking challenge:"
msgstr "Prepare-se para o maior desafio multitarefa:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer_controls.xml:0
msgid "Press Ctrl to duplicate the shift"
msgstr "Pressione Ctrl para duplicar o turno"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__module_project_forecast
msgid "Project Planning"
msgstr "Planejamento de projeto"

#. module: planning
#: model:planning.role,name:planning.planning_role_projector
msgid "Projector"
msgstr "Projetor"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__slot_properties
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Properties"
msgstr "Propriedades"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Publish"
msgstr "Publicar"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_publish_and_send
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Publish & Send"
msgstr "Publicar e enviar"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Publish & Send the Schedule by Email"
msgstr "Publicar e enviar a programação por e-mail"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__published
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__published
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Published"
msgstr "Publicado"

#. module: planning
#: model:hr.job,name:planning.job_quality_control
#: model:planning.role,name:planning.planning_quality_control
msgid "Quality Control Inspector"
msgstr "Inspetor de controle de qualidade"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr "Taxa de geração de turnos"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrence_update
msgid "Recurrence Update"
msgstr "Atualizar recorrência"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__recurrency_id
msgid "Recurrency"
msgstr "Recorrência"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Recurring Shifts"
msgstr "Turnos recorrentes"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Recurring shifts created"
msgstr "Turnos recorrentes criados"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr "Entradas de planejamento relacionadas"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuário relacionado para o gerente controlar seus acessos."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Remove"
msgstr "Remover"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "Repetir"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "Repetir a cada"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr "Tipo de repetição"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_unit
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_unit
msgid "Repeat Unit"
msgstr "Unidade de repetição"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "Repetir até"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr "Repetir todo"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_number
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_number
msgid "Repetitions"
msgstr "Repetições"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "Relatórios"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Requests to Switch"
msgstr "Solicitações de troca"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_reset_to_draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Reset to Draft"
msgstr "Voltar para provisório"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Resource"
msgstr "Recurso"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_color
msgid "Resource color"
msgstr "Cor do recurso"

#. module: planning
#: model:ir.model,name:planning.model_resource_resource
#: model:ir.model.fields,field_description:planning.field_planning_role__resource_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
msgid "Resources"
msgstr "Recursos"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Role"
msgstr "Função"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__default_planning_role_id
msgid ""
"Role that will be selected by default when creating a shift for this employee.\n"
"This role will also have precedence over the other roles of the employee when planning orders."
msgstr ""
"Função que será selecionada por padrão ao criar um turno para esse funcionário.\n"
"Esta função também terá preferência sobre as outras funções do funcionário ao planejar ordens."

#. module: planning
#: model:ir.model.fields,help:planning.field_resource_resource__default_role_id
msgid ""
"Role that will be selected by default when creating a shift for this resource.\n"
"This role will also have precedence over the other roles of the resource when planning shifts."
msgstr ""
"Função que será selecionada por padrão ao criar um turno para esse recurso.\n"
"Essa função também terá preferência sobra as outras funções do recurso ao planejar turnos."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Role:"
msgstr "Função:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_roles
#: model:ir.model.fields,field_description:planning.field_resource_resource__role_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Roles"
msgstr "Funções"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__planning_role_ids
msgid ""
"Roles that the employee can fill in. When creating a shift for this employee, only the shift templates for these roles will be displayed.\n"
"Similarly, only the open shifts available for these roles will be sent to the employee when the schedule is published.\n"
"Additionally, the employee will only be assigned orders for these roles (with the default planning role having precedence over the other ones).\n"
"Leave empty for the employee to be assigned shifts regardless of the role."
msgstr ""
"Funções que o funcionário pode cumprir. Ao criar um turno para esse funcionário, somente os modelos de turno dessas funções serão exibidos.\n"
"Do mesmo modo, somente os turnos abertos disponíveis para estas funções serão enviados para o funcionário quando a agenda for publicada.\n"
"Além disso, o funcionário só será atribuído a ordens dessas funções (e a função padrão de planejamento tem preferência sobre as outras).\n"
"Deixe em branco para que o funcionário seja atribuído a turnos independentemente da função."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "Salvar"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Save & Send Schedule"
msgstr "Salvar e enviar agenda"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save Template"
msgstr "Salvar modelo"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_creation
msgid "Save as Template"
msgstr "Salvar como modelo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Save this shift once it is ready."
msgstr "Salve este turno quando estiver pronto."

#. module: planning
#: model:planning.role,name:planning.planning_role_scanner
msgid "Scanner"
msgstr "Scanear"

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "Agenda"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_resource
msgid "Schedule by Resource"
msgstr "Agenda por recurso"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr "Agenda por função"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "Schedule sent to your employees"
msgstr "Cronograma enviado a seus funcionários"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Programe seus recursos humanos e materiais entre funções, projetos e pedidos"
" de vendas."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Search operation not supported"
msgstr "Operação de busca não suportada"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
msgid "Security Token"
msgstr "Token de segurança"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "See conflicting shifts"
msgstr "Ver turnos conflitantes"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Send"
msgstr "Enviar"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr "Enviar planejamento"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Send schedule"
msgstr "Enviar agenda"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "Definições"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Share the schedule with your team by publishing and sending it. Open the "
"menu to access this option."
msgstr ""
"Compartilhe a programação com sua equipe publicando-a e enviando-a. Abra o "
"menu para acessar essa opção."

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Shift"
msgstr "Turno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr "Lista de turnos"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr "Modelo de turno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr "Formulário do modelo de turno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr "Lista de modelos de turno"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr "Modelos de turno"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Shift saved as template"
msgstr "Turno salvo como modelo"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shift sent"
msgstr "Turno enviado"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts Planned"
msgstr "Turnos planejados"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr "Turnos de"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts in Conflict"
msgstr "Turnos em conflito"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Department Member"
msgstr "Turnos do membro do seu departamento"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Team Member"
msgstr "Turnos do membro da sua equipe"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shifts reset to draft"
msgstr "Turnos redefinidos como rascunho"

#. module: planning
#: model:planning.role,name:planning.planning_shipping_associate
msgid "Shipping Associate"
msgstr "Parceiro de frete"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr "Algumas alterações foram feitas desde a publicação do turno."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Span"
msgstr "Intervalo"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Start"
msgstr "Iniciar"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Start & End Hours"
msgstr "Horas de início e de término"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Start Date"
msgstr "Data de início"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__state
#: model:ir.model.fields,field_description:planning.field_planning_slot__state
msgid "Status"
msgstr "Status"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr "Data de encerramento"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__switch
msgid "Switch shifts with other employees"
msgstr "Troca de turnos com outros funcionários"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_res_company_planning_self_unassign_days_before_positive
msgid ""
"The amount of days before unassignment must be positive or equal to zero."
msgstr ""
"A quantidade de dias antes da retirada da atribuição deve ser positiva ou "
"igual a zero."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The company does not allow you to unassign yourself from shifts."
msgstr "A empresa não permite que você se retire suas atribuições de turnos."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The deadline for unassignment has passed."
msgstr "O prazo final para retirar a atribuição passou."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "The end date of a shift should be after its start date."
msgstr ""
"A data de término de um turno deve ser posterior a sua data de início."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "The number of repetitions cannot be negative."
msgstr "O número de repetições não pode ser negativo."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "The recurrence should be greater than 0."
msgstr "A recorrência deve ser maior que 0."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The recurrence's end date should fall after the shift's start date."
msgstr ""
"A data de término da recorrência deve ser posterior à data de início do "
"turno."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "The shift has successfully been created."
msgstr "O turno foi criado com sucesso."

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/static/src/views/planning_hooks.js:0
#: code:addons/planning/wizard/planning_send.py:0
msgid ""
"The shifts have already been published, or there are no shifts to publish."
msgstr "Os turnos já foram publicados ou não há turnos para publicar."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The shifts have successfully been published and sent."
msgstr "Os turnos foram publicados e enviados com sucesso."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_days_positive
msgid "The span must be at least 1 working day."
msgstr "O intervalo deve ser de pelo menos 1 dia útil."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start and end hours must be greater or equal to 0 and lower than 24."
msgstr ""
"As horas de início e de fim devem ser maiores ou iguais a 0 e menores que "
"24."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "The start hour cannot be greater than 24."
msgstr "A hora de início não pode ser superior a 24."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "The start hour cannot be negative."
msgstr "A hora de início não pode ser negativa."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start hour cannot be before the end hour for a one-day shift template."
msgstr ""
"A hora de início não pode ser antes da hora de término para um modelo de "
"turno de um dia."

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "The work email is missing for the following employees:"
msgstr "O e-mail de trabalho está faltando para os seguintes funcionários:"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no resources available for this open shift."
msgstr "Não há recursos disponíveis para esse turno aberto."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"There are no shifts planned for the previous week, or they have already been"
" copied."
msgstr ""
"Não há turnos planejados para a semana anterior ou eles já foram copiados."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to publish and send."
msgstr "Não há turnos para publicar e enviar."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to reset to draft."
msgstr "Não há turnos para redefinir como provisório."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This Progress Bar is not implemented."
msgstr "Esta barra de progresso não foi implementada."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr "Este turno foi copiado da semana anterior"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "This Week"
msgstr "Esta Semana"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__subsequent
msgid "This and following shifts"
msgstr "Turno atual e seguintes"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"This employee is not expected to work during this period, either because "
"they do not have a current contract or because they are on leave."
msgstr ""
"Não se espera que esse funcionário trabalhe durante esse período, seja "
"porque não há contrato vigente ou porque está de licença."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This method must take two slots in argument."
msgstr "Esse método deve receber dois slots como argumento."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"This open shift is no longer available, or the planning has been updated in "
"the meantime. Please contact your manager for further information."
msgstr ""
"Este turno aberto não está mais disponível ou o planejamento foi atualizado "
"enquanto isso. Entre em contato com seu gerente para obter mais informações."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__this
msgid "This shift"
msgstr "Este turno"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "This shift is recurrent. Delete:"
msgstr "Esse turno é recorrente. Excluir:"

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr "Dica: Registre seu planejamento mais rapidamente"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat
msgid ""
"To avoid polluting your database and performance issues, shifts are only "
"created for the next 6 months. They are then gradually created as time "
"passes by in order to always get shifts 6 months ahead. This value can be "
"modified from the settings of Planning, in debug mode."
msgstr ""
"Para evitar poluir sua base de dados e causar problemas de desempenho, são "
"criados turnos somente para os próximos seis meses. Então, eles são criados "
"gradualmente com o passar do tempo de modo que sempre haja turnos para os "
"próximos seis meses. Esse valor pode ser modificado pelas definições de "
"Planejamento, com o modo desenvolvedor."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Today"
msgstr "Hoje"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Total"
msgstr "Total"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_type
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_type
msgid "Type"
msgstr "Tipo"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__unassign
msgid "Unassign themselves from shifts"
msgstr "Retirar sua atribuição de turnos"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "Até"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Unwanted Shifts Available"
msgstr "Turnos recusados disponíveis"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr "Até qual data os planejamentos devem ser repetidos"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__user_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "Usuário"

#. module: planning
#. odoo-python
#: code:addons/planning/models/hr.py:0
msgid "View Planning"
msgstr "Exibir planejamento"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Waiter"
msgstr "Garçom"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Week"
msgstr "Semana"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__week
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__week
msgid "Weeks"
msgstr "Semanas"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "E-mail profissional"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_location_id
msgid "Work Location"
msgstr "Local de trabalho"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Working Days"
msgstr "Dias de trabalho"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Write the <b>role</b> your employee will perform (<i>e.g. Chef, Bartender, "
"Waiter, etc.</i>). <i>Tip: Create open shifts for the roles you will be "
"needing to complete a mission. Then, assign those open shifts to the "
"resources that are available.</i>"
msgstr ""
"Escreva a <b>função</b> que seu funcionário realizará (<i>Ex: chef, "
"bartender, garçom, etc</i>.). <i>Dica: Crie turnos abertos para as funções "
"necessárias para cumprir uma missão. Então, atribua esses turnos abertos aos"
" recursos disponíveis.</i>"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__year
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__year
msgid "Years"
msgstr "Anos"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You are not allowed to reset shifts to draft."
msgstr "Você não tem permissão para redefinir turnos para o rascunho."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not assign yourself to an already assigned shift."
msgstr "Você não pode atribuir um turno já atribuído a si mesmo."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not unassign another employee than yourself."
msgstr "Você não pode retirar a atribuição de outro funcionário."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot assign yourself to a shift in the past."
msgstr "Você não pode se atribuir a um turno no passado."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch made by another user."
msgstr ""
"Não é possível cancelar uma solicitação de troca feita por outro usuário."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch that is in the past."
msgstr "Você não pode cancelar uma solicitação de troca que já passou."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot request to switch a shift that is assigned to another user."
msgstr ""
"Você não pode solicitar a troca de um turno que está atribuído a outro "
"usuário."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot switch a shift that is in the past."
msgstr "Você não pode trocar um turno que já passou."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot unassign yourself from a shift in the past."
msgstr "Você não pode retirar sua atribuição de um turno no passado."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "You don't have any shifts planned yet."
msgstr "Você ainda não tem nenhum turno planejado."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid ""
"You don't have any shifts planned yet. You can assign yourself some of the "
"available open shifts."
msgstr ""
"Você ainda não tem nenhum turno planejado. Você pode atribuir a si mesmo "
"alguns dos turnos abertos disponíveis."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to assign yourself to shifts."
msgstr "Você não tem direito de fazer atribuições de turnos a si mesmo."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to cancel a request to switch."
msgstr "Você não tem direito de cancelar uma solicitação de troca."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to switch shifts."
msgstr "Você não tem direito de trocar turnos."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr "Sua atribuição a este turno foi retirada com sucesso."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr "Seu planejamento"

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from {{ format_date(ctx.get('start_datetime')) }} to {{ "
"format_date(ctx.get('end_datetime')) }}"
msgstr ""
"Seu planejamento de {{ format_date(ctx.get('start_datetime')) }} até {{ "
"format_date(ctx.get('end_datetime')) }}"

#. module: planning
#: model:mail.template,subject:planning.email_template_shift_switch_email
msgid ""
"Your shift on {{ ctx.get('start_datetime') }} was re-assigned to {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"
msgstr ""
"Seu turno em {{ ctx.get('start_datetime') }} foi reatribuído a {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "all shifts"
msgstr "todos os turnos"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "close"
msgstr "encerrar"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "days before the beginning of the shift"
msgstr "dias antes do início do turno"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "e.g. Bartender"
msgstr "ex: bartender"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. Cleaner"
msgstr "ex: faxineiro"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_form_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_tree_view_inherit
msgid "e.g. Crane"
msgstr "ex: guindaste"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. John Doe"
msgstr "ex.: João da Silva"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr "outro(s) turno(s) em conflito."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this and following shifts"
msgstr "turno atual e seguintes"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this shift"
msgstr "turno atual"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr "até"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
msgstr ""
"{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
