# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Work Order: </strong>"
msgstr ""

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "Kontrole"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
msgid "Component Lot/Serial"
msgstr "Lot/Serijski broj komponente"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Create a Quality Alert"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Discard"
msgstr "Odbaci"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.mrp_production_workorder_tree_editable_view_inherit_quality
msgid "Done"
msgstr "Gotovo"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Fail"
msgstr "Pao"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Failure"
msgstr "Greška"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_quality_mrp_workorder
msgid "Finished Lot/Serial"
msgstr "Dovršeni lot/serijski broj"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_lot
msgid "Lot/Serial"
msgstr "Lot/Serija"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "Proizvodni nalog"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Manufacturing Steps"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Measure:"
msgstr "Mjera:"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_quality_check_confirmation_dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Open spreadsheet"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_search_inherit_quality_mrp_workorder
msgid "Operation"
msgstr "Operacija"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Pass"
msgstr "Prošao"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_template
msgid "Product"
msgstr "Proizvod"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Skladišna kretanja proizvoda(stavke)"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_product
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "Test kvalitete"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Quality Check Failed"
msgstr "Provjera nije zadovoljena"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:ir.actions.act_window,name:quality_mrp_workorder.quality_check_action_wo
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
msgid "Quality Checks"
msgstr "Provjere kvalitete"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "Točka kontrole kvalitete"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Quality Points"
msgstr "Bodovi kvalitete"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Save"
msgstr "Spremi"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_quality_check__operation_id
msgid "Step"
msgstr "Korak"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Success"
msgstr "Uspjeh"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The On-demand frequency is not possible with work order quality points."
msgstr ""

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Validate"
msgstr "Odobri"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "Radni nalog"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_tree_inherit_mrp_workorder
msgid "Work Order Operation"
msgstr "Operacija radnog naloga"
