# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.worksheet_page
msgid "<strong>Work Order: </strong>"
msgstr ""

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr "Šeky"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
msgid "Component Lot/Serial"
msgstr ""

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Create a Quality Alert"
msgstr "Vytvořit výstrahu na kvalitu"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Discard"
msgstr "Zrušit"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.mrp_production_workorder_tree_editable_view_inherit_quality
msgid "Done"
msgstr "Hotovo"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Fail"
msgstr "Selhalo"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Failure"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_quality_mrp_workorder
msgid "Finished Lot/Serial"
msgstr "Dokončené výrobní / sériové číslo"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_lot
msgid "Lot/Serial"
msgstr "Šarže/sériové číslo"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_production
msgid "Manufacturing Order"
msgstr "Výrobní příkaz"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Manufacturing Steps"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Measure:"
msgstr ""

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_quality_check_confirmation_dialog/mrp_quality_check_confirmation_dialog.xml:0
msgid "Open spreadsheet"
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_search_inherit_quality_mrp_workorder
msgid "Operation"
msgstr "Úkon"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Pass"
msgstr "Prošlo"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (položka pohybu zásob)"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_product_product
msgid "Product Variant"
msgstr "Produktová varianta"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr "Kontrola kvality"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Quality Check Failed"
msgstr "Kontrola kvality selhala"

#. module: quality_mrp_workorder
#. odoo-javascript
#: code:addons/quality_mrp_workorder/static/src/mrp_display/mrp_display_record.xml:0
#: model:ir.actions.act_window,name:quality_mrp_workorder.quality_check_action_wo
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_search_inherit_mrp_workorder
msgid "Quality Checks"
msgstr "Výstupy kontrol"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr "Bod kontroly kvality"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_point_view_search_inherit_mrp_workorder
msgid "Quality Points"
msgstr "Body kvality"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_alert_view_form_inherit_mrp
msgid "Save"
msgstr "Uložit"

#. module: quality_mrp_workorder
#: model:ir.model.fields,field_description:quality_mrp_workorder.field_quality_check__operation_id
msgid "Step"
msgstr "Krok"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid "Success"
msgstr "Úspěch"

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The On-demand frequency is not possible with work order quality points."
msgstr ""

#. module: quality_mrp_workorder
#. odoo-python
#: code:addons/quality_mrp_workorder/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr ""

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_tablet_inherit_quality
msgid "Validate"
msgstr "Potvrdit"

#. module: quality_mrp_workorder
#: model:ir.model,name:quality_mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr "Pracovní příkaz"

#. module: quality_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_form_inherit_mrp_workorder
#: model_terms:ir.ui.view,arch_db:quality_mrp_workorder.quality_check_view_tree_inherit_mrp_workorder
msgid "Work Order Operation"
msgstr "Operace pracovního příkazu"
