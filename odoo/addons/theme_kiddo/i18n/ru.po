# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* theme_kiddo
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-07 13:29+0000\n"
"PO-Revision-Date: 2024-09-25 18:03+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quotes_carousel_minimal
msgid ""
"\" A perfect place for early growth. <br/>Supportive staff, fun activities, "
"and a loving environment. \""
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quotes_carousel_minimal
msgid ""
"\" Exceptional teachers and care! <br/>They foster learning and joy every "
"single day. \""
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quotes_carousel_minimal
msgid ""
"\" Their care and learning are wonderful. Safe, nurturing, and full of "
"creativity. \""
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$10.00"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$12.00"
msgstr "$12.00"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$15.00"
msgstr "$15.00"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$35.00"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$7.00"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "$8.00"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_call_to_action
msgid "2,000 parents<br/> brought their kid to our nursery."
msgstr "2 000 родителей<br/> привели своего ребенка в наш детский сад."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_big_number
msgid "80+"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"<br/>\n"
"        For every kid"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"<br/>\n"
"        Open-door policy"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"<br/>\n"
"        Our mission"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_text_cover
msgid ""
"<br/>Discover our nursery’s welcoming environment and engaging programs with"
" ease. Our platform helps you explore our activities, meet our staff, and "
"see how we make early learning fun and enriching for your child.<br/>"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid ""
"<br/>Where every child blossoms in a safe and loving environment.<br/><br/>"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid ""
"<i class=\"fa fa-fw fa-info-circle o_not-animable\" role=\"img\"/>  Caring "
"hands"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_title
msgid "A Deep Dive into Play and Learning"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "A Fun and Safe Environment for Your Little One"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_framed_intro
msgid "A Safe Place to Grow: Play, Learn, and Thrive"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "A caring and supportive atmosphere for early development"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_banner
msgid "A little place <br/>of paradise"
msgstr "Маленький уголок <br/>рая"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_hexagonal
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_sidegrid
msgid "A little place of paradise"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_top
msgid "A little place of paradise."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_intro_pill
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_product_list
msgid "About us"
msgstr "О нас"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_text
msgid "Activities"
msgstr "Активность"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"All activities are planned to meet each child’s individual needs. This "
"approach will help your child to develop to the best of their ability."
msgstr ""
"Все занятия планируются с учетом индивидуальных потребностей каждого "
"ребенка. Такой подход поможет вашему ребенку развиваться наилучшим образом."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid "Around-the-Clock Support"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Backpack"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid "Caring for Your Child's Growth and Happiness"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Colorful set of crayons and markers for creative drawing and coloring "
"activities in the classroom."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Compact pencil case with compartments for pens, pencils, and other small "
"stationery items."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_call_to_action
msgid "Contact us"
msgstr "Свяжитесь с нами"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Crayons and Markers Set"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_unveil
msgid ""
"Create joyful memories and foster growth in a loving, playful environment."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid "Creative Play"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_images_mosaic
msgid "Discover fun and engaging activities designed for young learners."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid "Discover joyful learning"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cta_box
msgid ""
"Discover our wide range of destinations for children holidays<br/><br/>"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_intro_pill
msgid "Discover our<br/>Little World"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_text_cover
msgid "Discover<br/>our nursery"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_center_top
msgid ""
"Dive into a world where playgrounds come to life and costumes turn dreams "
"into reality, sparking endless adventures for kids of all ages."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Durable backpack with adjustable straps and multiple compartments for "
"organizing school essentials."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_shape_image
msgid "Early childhood care"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid "Engaging Learning Activities"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "Engaging activities that spark curiosity and creativity"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid ""
"Enjoy convenient and adaptable care schedules to fit your family’s needs, "
"from full-day to part-time and drop-in services."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid ""
"Enrich your child's early years with our fun programs and dedicated "
"teachers."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid "Enriching Activities"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid ""
"Ensure your child’s well-being in a secure, caring setting with experienced "
"staff dedicated to providing personalized attention and support."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_call_to_action
msgid "Entrust us with your children and go to work with peace of mind"
msgstr "Доверьте нам своих детей и спокойно отправляйтесь на работу"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Equip your child with all the necessary supplies for a successful school "
"year. Each item is selected to ensure both comfort and functionality."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Essential School Supplies"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_product_list
msgid "Events"
msgstr "События"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid "Experiences designed just for kids"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid ""
"Explore more and find a nurturing space for your child’s growth, designed to"
" inspire and support their early development."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_center_top
msgid "Explore the Fun"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid "Flexible Scheduling Options"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid ""
"Foster creativity and curiosity with our engaging activities and caring "
"environment, perfect for your child's first steps."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid ""
"Foster early development with interactive and educational activities "
"designed to stimulate curiosity and creativity in young children."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid "Fun Learning Activities"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid "Fun and Educational Activities"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cta_box
msgid "Give your child<br/>unforgettable memories"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid "Growth and Exploration"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_top
msgid "Happy Kids, Happy Parents: Great Childcare"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quadrant
msgid ""
"Ignite your child's imagination with our range of toys and games. Designed "
"for creativity and fun, our products bring joy to kids of all "
"ages.<br/><br/> Explore the magic of playtime."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_center_top
msgid "Imaginative Playgrounds, Games and Costumes for Kids"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_card_offset
msgid ""
"Inspire creativity and joy in children with our carefully curated selection "
"of toys and games. Designed for endless fun and learning, our products are "
"perfect for every child."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_freegrid
msgid ""
"Inspire imagination and creativity with our range of toys and games for kids"
" of all ages. Our products are designed to engage young minds, offering "
"endless hours of fun and learning."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Insulated lunchbox with a secure closure and easy-to-clean interior, perfect"
" for keeping meals fresh."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cover
msgid "Kiddo Nursery"
msgstr "Питомник Kiddo"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid "Learn about our approach   <i class=\"fa fa-long-arrow-right\" role=\"img\"/>"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_text_cover
msgid "Learn more"
msgstr "Узнать больше"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_picture
msgid "Letting each child explore and grow at their own pace"
msgstr ""
"Позволяя каждому ребенку исследовать и развиваться в своем собственном темпе"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Lunchbox"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Notebook"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid "Nurturing Early Learning"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "Nurturing Young Minds with Joyful Learning"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid "Nurturing growth<br/>at Little Sprouts Nursery"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_carousel_intro
msgid "Nurturing young minds through play"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_images_mosaic
msgid "Our New Learning Programs"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"Our aim is to make the early years of your child’s life as enjoyable, "
"rewarding and positive as possible."
msgstr ""
"Наша цель - сделать первые годы жизни вашего ребенка как можно более "
"приятными, полезными и позитивными."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid ""
"Our dedicated staff is here to support you and your child, offering guidance"
" and assistance whenever you need it."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cta_box
msgid "Our destinations"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid ""
"Our facilities are designed with your child’s safety in mind. With secure "
"premises, child-friendly spaces, and vigilant supervision, we provide peace "
"of mind for parents and a joyful experience for children."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_shape_image
msgid ""
"Our nursery provides a nurturing environment where young children can grow "
"and learn. With engaging activities and a focus on development, we ensure a "
"supportive start to each child's educational journey."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid "Our programs"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_picture
msgid ""
"Our qualified staff ensure that each childs' needs are always recognised and"
" fulfilled."
msgstr ""
"Наш квалифицированный персонал гарантирует, что потребности каждого ребенка "
"всегда будут учтены и удовлетворены."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_product_list
msgid "Our team"
msgstr "Наша команда"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_text
msgid "Our team is composed of experienced nursery nurses."
msgstr "Наша команда состоит из опытных медсестер."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid ""
"Our team of trained caregivers and educators is dedicated to providing "
"personalized attention and care. We focus on creating a warm and supportive "
"atmosphere where every child feels valued and understood."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Pencil Case"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid "Personalized Care Plans"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "Personalized attention to help every child thrive"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_product_list
msgid "Playground"
msgstr "Игровая площадка"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_card_offset
msgid "Playtime just got better."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid "Professional and Caring Staff"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid "Quality and Nurturing"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Reusable water bottle with a spill-proof lid and easy-grip design, ideal for"
" staying hydrated throughout the day."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_features_wall
msgid "Safe &amp; Nurturing Environment"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid "Safe and Secure Environment"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_empowerment
msgid "Schedule a visit"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quadrant
msgid "Shop Now"
msgstr "Купить сейчас"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_freegrid
msgid "Shop Toys"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid ""
"Spiral-bound notebook with lined pages, perfect for taking notes and "
"practicing writing skills."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_images
msgid "Start your child's learning journey with us today"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cover
msgid ""
"The countryside nursery since 2002.<br/>\n"
"        A truly unique service in a highly secure and tranquil setting."
msgstr ""
"Загородный питомник с 2002 года.<br/>\n"
"        По-настоящему уникальный сервис в очень безопасном и спокойном месте."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_sidegrid
msgid ""
"The countryside nursery since 2002.<br/>A truly unique service in a highly "
"secure and tranquil setting."
msgstr ""

#. module: theme_kiddo
#: model:ir.model,name:theme_kiddo.model_theme_utils
msgid "Theme Utils"
msgstr "Тематические утилиты"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_text
msgid ""
"They will propose to your children educational activities, playful, varied "
"and adapted to the stage of development."
msgstr ""
"Они предложат вашим детям развивающие занятия, игровые, разнообразные и "
"адаптированные к уровню развития."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Это простой блок героя, компонент в стиле джамботрона для привлечения "
"дополнительного внимания к содержанию или информации."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_picture
msgid ""
"Through stimulating and fun activities, our aim<br/>\n"
"        is to promote all areas of a childs' development."
msgstr ""
"Благодаря стимулирующим и веселым занятиям наша цель<br/>\n"
"        способствовать развитию всех сфер ребенка."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_quadrant
msgid "Toys &amp; Fun"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_card_offset
msgid "Toys That Spark Imagination"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_freegrid
msgid "Toys and Games That Spark Creativity"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_image_title
msgid ""
"Transform your child’s day with our engaging programs, where fun meets "
"discovery. Elevate their growth with activities that blend playfulness and "
"learning seamlessly."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_product_list
msgid "Visit us"
msgstr "Посетите нас"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "Water Bottle"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid ""
"We create engaging and playful experiences tailored to each child's needs. "
"Our caring teachers provide a safe and stimulating environment for early "
"growth and exploration."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid ""
"We offer a range of activities that combine fun with learning, from creative"
" arts and crafts to outdoor play and interactive storytelling, ensuring "
"every child enjoys a balanced and enriching day."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_cards_grid
msgid ""
"We provide a safe and engaging environment where children can explore, "
"learn, and grow. Our programs are designed to foster creativity, curiosity, "
"and a love for learning from an early age."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid ""
"We provide hands-on activities and creative projects to help your child "
"learn and grow. Using innovative methods, we make every day a new adventure "
"in discovery."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid ""
"We provide tailored care and learning programs that cater to the unique "
"needs of each child, fostering their growth and development."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid "What we offer to your little ones"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_striped_center_top
msgid "Where every child becomes the hero of their story"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_unveil
msgid "Where smiles grow"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid ""
"With experienced caregivers and a love for teaching, we offer a curriculum "
"that nurtures every child’s curiosity and supports their early development."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_three_columns
msgid ""
"You are welcome to come and see the nursery and meet the team at any time "
"convenient to you."
msgstr ""
"Мы приглашаем вас посетить детский сад и познакомиться с командой в любое "
"удобное для вас время."

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_key_benefits
msgid ""
"Your child will enjoy a variety of carefully curated activities designed to "
"nurture creativity, curiosity, and social skills."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_wavy_grid
msgid ""
"Your child's happiness is our focus. Our friendly staff ensures a joyful and"
" interactive learning experience with plenty of fun activities and games."
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_big_number
msgid "families trusted us"
msgstr ""

#. module: theme_kiddo
#. odoo-javascript
#: code:addons/theme_kiddo/static/src/js/tour.js:0
msgid "width"
msgstr "ширина"

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "✽  Provided by the School"
msgstr ""

#. module: theme_kiddo
#: model_terms:theme.ir.ui.view,arch:theme_kiddo.s_pricelist_boxed
msgid "✽  School Supplies"
msgstr ""
