# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON>@gmail.com>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosavl<PERSON><PERSON>@gmail.com>, "
"2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Odsutan"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Kanal"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "IM status"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Zadnja dojava"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Zadnji put prisutan"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Poslednje izmenio/la"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Poslednje ažuriranje dana"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Poruka"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr ""

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Offline"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "Online"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "Refresh"
msgstr ""

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Korisnik"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Prisustvo korisnika"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Korisnici"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted "
"users it is important to change it immediately for security reasons. I will "
"keep nagging you about it!"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr ""
