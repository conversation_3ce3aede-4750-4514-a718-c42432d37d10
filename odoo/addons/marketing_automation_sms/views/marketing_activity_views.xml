<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="marketing_activity_view_form" model="ir.ui.view">
        <field name="name">marketing.activity.view.form</field>
        <field name="model">marketing.activity</field>
        <field name="inherit_id" ref="marketing_automation.marketing_activity_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//label[@for='mass_mailing_id']" position="after">
                <label for="mass_mailing_id" string="SMS Template" invisible="activity_type != 'sms'"/>
            </xpath>
            <xpath expr="//field[@name='mass_mailing_id']" position="attributes">
                <attribute name="invisible">activity_type not in ['email', 'sms']</attribute>
                <attribute name="required">activity_type in ['email', 'sms']</attribute>
            </xpath>
        </field>
    </record>
</odoo>
