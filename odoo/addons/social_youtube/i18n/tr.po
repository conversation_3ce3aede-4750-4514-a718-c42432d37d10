# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
# Translators:
# emre oktem, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin <PERSON>gaux, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Tunç Sabah, 2024
# <PERSON><PERSON>_<PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Deniz Guvener_Odoo <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"YouTube API tarafından sağlanan \"Yüklemeler\" Oynatma Listesi ID, bu asla "
"manuel olarak ayarlanmamalıdır."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr "123 Görüntüleme •"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-o-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-o-up me-1\" title=\"Beğeniler\"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr "<span class=\"fw-bold\">YouTube Kanalınız</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""
"<span>Bunlar 30 güne kadar saklanır ve size gerçekliğin doğru bir tasvirini sağlamak için sık sık yenilenir.</span>\n"
"<span>Bunları Odoo'dan silmek için bu hesabı silmeniz yeterlidir.</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr "Hesabınıza erişim herhangi bir zamanda iptal edilebilir"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr ""
"YouTube API tarafından sağlanan Access token, bu asla manuel olarak "
"ayarlanmamalıdır."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr "Hesap"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid "An error occurred."
msgstr "Bir hata oluştu."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr ""
"Yetkilendirme uç noktası, yenileme token sağlamadı. Lütfen tekrar deneyin."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Author Image"
msgstr "Üretici Resmi"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr "İptal"

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr "Kanal"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Clear"
msgstr "Temizle"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Comments"
msgstr "Yorumlar"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""
"Bu videodaki yorumlar \"devre dışı\" olarak işaretlenmiştir. Yorumlar "
"\"özel\" olarak işaretlenmiş olabilir. "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Confirmation"
msgstr "Doğrulama"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr "YouTube API tarafından döndürülen videonun kimliğini içerir"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr ""
"YouTube API tarafından döndürülen video kategorisinin kimliğini içerir"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""
"Hesabınız iptal edilemedi.\n"
"Hata:%s"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Description cannot exceed 5000 characters."
msgstr "Açıklama 5000 karakteri geçemez."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Description should not contain > or < symbol."
msgstr "Açıklama > veya < sembollerini içermemelidir."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Do you also want to remove the video from your YouTube account?"
msgstr "Videoyu YouTube hesabınızdan da kaldırmak istiyor musunuz?"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""
"YouTube API tarafından sağlanan Erişim Simgesinin sona erme tarihi, bu asla "
"manuel olarak ayarlanmamalıdır."

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr "Google Erişim Simgesi"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr "Google Gizlilik Politikası"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr "Google Refresh Token"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr "ID"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Likes"
msgstr "Beğeniler"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr "Medya Türü"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "No"
msgstr "Hayır"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr "OAuth Client ID"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr "OAuth Client Secret"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""
"Odoo, YouTube hesabınıza erişimi kaybedecek ve \n"
"ilgili tüm verileri veritabanınızdan silecektir."

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_privacy
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr ""
"Yayınlandıktan sonra videoyu Herkese Açık/Gizli/Liste Dışı olarak ayarlayın"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Our YouTube Social application uses YouTube API Services.\n"
"                        By using it, you implicitly agree to the:"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Please select a single YouTube account at a time."
msgstr "Lütfen tek seferde sadece bir tane YouTube hesabı seçin."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr "Özel"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Processing..."
msgstr "İşleme..."

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr "Genel"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Read More about YouTube Channel"
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Reason:"
msgstr "Sebep:"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"YouTube API tarafından sağlanan yenileme tokenı, bu asla manuel olarak "
"ayarlanmamalıdır."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr "Geriye al"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr "İptal Et ve Sil"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
msgid "Revoke Account"
msgstr "Hesabı İptal Et"

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr "YouTube Hesabını İptal Et"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr ""
"Erişim tokenı iptal etme şu anda yalnızca YouTube hesaplarıyla sınırlıdır."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Select"
msgstr "Seçme"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr "Seçilmiş Diğer Hesaplar"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr "Seçilmiş YouTube Hesapları"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""
"Videonun kendisi doğrudan YouTube'a yüklendiğinden, videonun dosya adını "
"tutmanız yeterlidir"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr "Sosyal Hesap"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr "Sosyal Canlı Gönderi"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr "Sosyal Medya"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr "Sosyal Gönderi"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr "Sosyal Gönderi Şablonu"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr "Sosyal Medya Akışı"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr "Sosyal Medya Yayını"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr "Seçilen video maksimum boyut olan %s'ı geçmektedir. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"Bu hizmetin istediği url hata verdi. Lütfen uygulamanın üreticisine "
"başvurun."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr "Paylaşmayı denediğiniz video YouTube'dan silinmiştir."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "There is no channel linked with this YouTube account."
msgstr "Bu YouTube hesabına bağlı hiçbir kanal yok."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Title cannot exceed 100 characters."
msgstr "Başlık 100 karakteri geçemez."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Title should not contain > or < symbol."
msgstr "Başlık > veya < sembollerini içermemelidir."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""
"Uygulama hizmetlerimizi sağlamak için YouTube hesabınızdan aşağıdaki "
"verileri sakladığımızı unutmayın:"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr "Token son kullanma tarihi"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr "Yetkisiz. Lütfen yöneticinizle iletişime geçin."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
msgid "Unknown"
msgstr "Bilinmeyen"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr "listelenmemiş"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Upload Video"
msgstr "Video Yükle"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Upload failed. Please try again."
msgstr "Yükleme başarısız. Lütfen tekrar deneyin."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Uploading... %s%"
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Uploading... 0%"
msgstr "Yükleniyor... %0"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr "Kişisel YouTube hesabınızı kullanın"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Bazı özellikleri belirli bir mecra ile sınırlamamız gerektiğinde "
"karşılaştırma yapmak için kullanılır ('facebook', 'x', ...)."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Video"
msgstr "Video"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr "Video Açıklaması"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_privacy
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr "Video Gizliliği"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr "Video Başlığı"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Video Upload"
msgstr "Video Yükleme"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid "Video not found. It could have been removed from Youtube."
msgstr "Video bulunamadı. YouTube'dan kaldırılmış olabilir."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Views"
msgstr "Görünümler"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Yes, delete it"
msgstr "Evet, sil"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You cannot use '>' or '<' in both title and description."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr ""
"Hesap bağlantısı kurulamıyor, lütfen sistem yöneticinize başvurun. Daha "
"Fazla Bilgi İçin : %s"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "You have to upload a video when posting on YouTube."
msgstr "YouTube'da paylaşırken bir video yüklemelisiniz."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You need to give your video a description."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You need to give your video a title."
msgstr ""

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr "YouTube Access Token"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr "YouTube Category Id"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr "YouTube Channel ID"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"YouTube API tarafından sağlanan YouTube Kanal ID, bu asla manuel olarak "
"ayarlanmamalıdır."

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
msgid "YouTube Comments"
msgstr "Youtube Yorumları"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr "YouTube Yorum Sayısı"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr "YouTube Geliştirici Hesabı"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr "YouTube Beğenmeme"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr "YouTube Beğenileri"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr "YouTube OAuth Client ID"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr "YouTube OAuth Client Secret"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr "YouTube Options"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placeholder"
msgstr ""

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr "YouTube önizleme"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr "YouTube Terms of Service (ToS)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr "YouTube Thumbnail"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr "YouTube Thumbnail Url"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr "YouTube Upload Playlist ID"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr "YouTube Video"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_description
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr "YouTube Video Açıklaması"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr "YouTube Video ID"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr "YouTube Video Id"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_title
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr "YouTube Video Başlığı"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr "YouTube Video Url"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr "YouTube Görüntülemeleri"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr ""
"YouTube geçerli bir access token sağlamadı veya süresi dolmuş olabilir."

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "YouTube did not provide a valid authorization code."
msgstr "YouTube geçerli bir authorization code sağlamadı."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr "Kanalınızın ismi ve resmi"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Your description cannot exceed 5000 characters."
msgstr ""

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Your title cannot exceed 100 characters."
msgstr ""

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
msgid "Your video is missing a correct title or description."
msgstr "Videonuzda doğru bir başlık veya açıklama eksik."

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr ""
"Başlık ve görüntüleme sayıları dahil olmak üzere videolarınızın meta "
"verileri (ancak videonun kendisi asla)"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""
"Örneğin. Tüm topluluğunuzu tek bir uygulama ile meşgul edin! "
"https://www.odoo.com/trial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr "e.g. Odoo Social Tutorial"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr "Google Third-party uygulama hesabı erişim paneli"
