# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span> إلى </span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "نموذج تحويل الحساب "

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "بند نموذج تحويل الحساب "

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
msgid "Account automatic transfers: Perform transfers"
msgstr "تحويلات الحساب التلقائية: أداء التحويلات "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "تفعيل"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__active
msgid "Active"
msgstr "نشط "

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these analytic accounts to the destination account"
msgstr ""
"يقوم بإضافة شرط لتحويل مجموع البنود من الحسابات الأصلية التي تطابق تلك "
"الحسابات التحليلية إلى الحساب الهدف فقط "

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these partners to the destination account"
msgstr ""
"يقوم بإضافة شرط لتحويل مجموع البنود من الحسابات الأصلية التي تطابق هؤلاء "
"الشركاء إلى الحساب الهدف فقط "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "عامل تصفية تحليلي "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_search
msgid "Archived"
msgstr "مؤرشف "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "التحويل التلقائي "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (%(percent)s%% from account %(origin_account)s)"
msgstr "التحويل التلقائي (%(percent)s%% من الحساب %(origin_account)s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (-%s%%)"
msgstr "التحويل التلقائي (-%s%%)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (entries with analytic account(s): %(analytic_accounts)s "
"and partner(s): %(partners)s)"
msgstr ""
"التحويل التلقائي (القيود التي بها حساب (حسابات) تحليلية: "
"%(analytic_accounts)s والشريك (الشركاء): %(partners)s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "التحويل التلقائي (القيود التي بها حساب (حسابات) تحليلية: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "التحويل التلقائي (القيود التي بها شريك (شركاء): %s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s and partner(s): %(partners)s)"
msgstr ""
"التحويل التلقائي (من الحساب %(origin_account)s الذي به حسابات تحليلية: "
"%(analytic_accounts)s والشريك (الشركاء): %(partners)s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s)"
msgstr ""
"التحويل التلقائي (من الحساب %(origin_account)s الذي به حسابات تحليلية: "
"%(analytic_accounts)s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with partner(s): "
"%(partners)s)"
msgstr ""
"التحويل التلقائي (من الحساب %(origin_account)s الذي به الشركاء: "
"%(partners)s) "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (to account %s)"
msgstr "التحويل التلقائي (للحساب %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
msgid "Automatic Transfers"
msgstr "التحويلات التلقائية "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "الشركة المتعلقة بهذه اليومية "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "حساب التحويل "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "الوصف"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "حساب الوجهة"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "حسابات الوجهة "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "يومية الوجهة "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "تعطيل"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "معطل"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "معدل الحدوث "

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "القيود المُنشأة "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "الحركات المُنشأة "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "المُعرف"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS Automatic Transfers"
msgstr "التحويلات التلقائية في IFRS "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS rent expense transfer"
msgstr "تحويل نفقات الإيجار في IFRS "

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_journal
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "شهرياً"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "تعداد مُعرفات الحركات "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "نموذج الحركة "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "نماذج الحركة "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "الاسم"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "يُسمح بوجود حساب واحد فقط لكل نموذج تحويل "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "الحسابات الأصلية "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
msgid "Originating Model"
msgstr "النموذج المُنشئ "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "عامل تصفية الشريك "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "بالمئة "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "النسبة (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "النسبة للقراءة فقط "

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid ""
"Percentage of the sum of lines from the origin accounts will be transferred "
"to the destination account"
msgstr ""
"سوف يتم تحويل نسبة من مجموع البنود من الحسابات الأصلية إلى حساب الوجهة "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "الفترة"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "جاري"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "الحالة "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "تاريخ الإيقاف "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The analytic filter %s is duplicated"
msgstr "تم إنشاء نسخة مطابقة من عامل التصفية التحليلي %s "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"The partner filter %(partner_filter)s in combination with the analytic "
"filter %(analytic_filter)s is duplicated"
msgstr ""
"تم إنشاء نسخة مطابقة لعامل تصفية الشريك %(partner_filter)s وجمعه مع الحساب "
"التحليلي %(analytic_filter)s "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The partner filter %s is duplicated"
msgstr "تم إنشاء نسخة مطابقة لعامل تصفية الشريك %s "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The total percentage (%s) should be less or equal to 100!"
msgstr "يجب أن تكون النسبة الكلية (%s) أقل من أو تساوي 100! "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "النسبة الكلية "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "نموذج التحويل "

#. module: account_auto_transfer
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "التحويلات "

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "سنويًا"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "Yearly liabilites auto transfers"
msgstr "التحويلات التلقائية للالتزامات السنوية "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has draft moves attached "
"('%s'). Please delete them before deleting this transfer."
msgstr ""
"لا يمكنك حذف شحنة تلقائية بها حركات مرفقة بحالة المسودة ('%s')؛ يرجى حذفها "
"قبل حذف هذه الشحنة. "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has posted moves attached "
"('%s')."
msgstr "لا يمكنك حذف شحنة تلقائية بها حركات مرفقة قد تم ترحيلها ('%s'). "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/account_move_line.py:0
msgid "You cannot set Tax on Automatic Transfer's entries."
msgstr "لا يمكنك تعيين ضريبة في قيود التحويل التلقائي. "

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr "مثال: التحويل الشهري للنفقات "
