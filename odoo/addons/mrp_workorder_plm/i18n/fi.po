# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_plm
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid ""
"BoM feedback for not found step: %(step)s (%(production)s - %(operation)s)"
msgstr ""
"Osaluettelon palaute vaiheesta, jota ei löydy: %(step)s (%(production)s - "
"%(operation)s)"

#. module: mrp_workorder_plm
#: model:ir.model.fields,help:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Defines the type of the quality control point."
msgstr "Määrittää laadunvalvontapisteen tyypin."

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_operation
msgid "ECO Routing Changes - Operation"
msgstr "Teknisen muutostilauksen (ECO) reitityksen muutokset - Toiminta"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_quality_point
msgid "ECO Routing Changes - Quality Point"
msgstr ""
"Teknisen muutostilauksen (ECO) reitityksen muutokset - laaduntarkastuspiste"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "ECO reititysmuutokset"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr "Tekninen muutostilaus (ECO)"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Instruction Changes"
msgstr "Ohjeiden muutokset"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid "Instruction Suggestions (%(wo_name)s)"
msgstr "Ohje-ehdotukset (%(wo_name)s)"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "Valmistustilaus"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
msgid "New Step Suggestion: %s"
msgstr "Uusi askelehdotus: %s"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Open Quality Point"
msgstr "Avoin laatutarkastuspiste"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_propose_change
msgid "Propose a change in the production"
msgstr "Ehdotetaan muutosta tuotantoon"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Quality Changes made on the quality point."
msgstr "Laatumuutokset on tehty laaduntarkastuspisteeseen"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_check
msgid "Quality Check"
msgstr "Laadun tarkistus"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_point
msgid "Quality Control Point"
msgstr "Laadunvalvontapiste"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__quality_point_id
msgid "Quality Point"
msgstr "Laatupiste"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__step
msgid "Step"
msgstr "Vaihe"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Step Type"
msgstr "Vaiheen tyyppi"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__title
msgid "Title"
msgstr "Otsikko"
