# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_edi_ubl
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Marian<PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid ", Email: %(email)s"
msgstr ", e-mail: %(email)s"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid ", Phone: %(phone)s"
msgstr ", telefono: %(phone)s"

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_edi_common
msgid "Common functions for EDI orders"
msgstr "Funzioni comuni per ordini EDI"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrieve Delivery Address with Details: { %s }"
msgstr "Impossibile recuperare i dettagli dell'indirizzo di consegna: { %s }"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrieve product for line '%s'"
msgstr "Impossibile recuperare il prodotto per la riga '%s'"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrive Customer with Details: { %s }"
msgstr "Impossibile recuperare i dettagli del cliente: { %s }  "

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Format used to import the invoice: %s"
msgstr "FOrmato utilizzato per importare la fattura: %s"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Insufficient details to extract Customer: { %s }"
msgstr "Dettagli non sufficienti per estrarre il cliente: { %s }"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Name: %(name)s, Vat: %(vat)s"
msgstr "Nome: %(name)s, IVA: %(vat)s"

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_order.py:0
msgid "Some information could not be imported"
msgstr "Non è stato possibile importare alcune informazioni"

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_edi_xml_ubl_bis3
msgid "UBL BIS Ordering 3.0"
msgstr "UBL BIS Ordinazione 3.0"
