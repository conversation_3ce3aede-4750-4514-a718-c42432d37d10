# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_edi_ubl
# 
# Translators:
# <PERSON><PERSON><PERSON> Goutoudi<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> Goutoudi<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid ", Email: %(email)s"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid ", Phone: %(phone)s"
msgstr ""

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_edi_common
msgid "Common functions for EDI orders"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrieve Delivery Address with Details: { %s }"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrieve product for line '%s'"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Could not retrive Customer with Details: { %s }"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Format used to import the invoice: %s"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Insufficient details to extract Customer: { %s }"
msgstr ""

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_edi_common.py:0
msgid "Name: %(name)s, Vat: %(vat)s"
msgstr ""

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_order
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: sale_edi_ubl
#. odoo-python
#: code:addons/sale_edi_ubl/models/sale_order.py:0
msgid "Some information could not be imported"
msgstr ""

#. module: sale_edi_ubl
#: model:ir.model,name:sale_edi_ubl.model_sale_edi_xml_ubl_bis3
msgid "UBL BIS Ordering 3.0"
msgstr ""
