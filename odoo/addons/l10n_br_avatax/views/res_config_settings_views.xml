<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.account.l10n.br.avatax</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <setting id="rounding_method" position="after">
                <setting id="l10n_br_edi_avatax_settings" string="AvaTax Brazil"
                         help="Automatically compute tax rates"
                         invisible="country_code != 'BR'"
                         documentation="/applications/finance/fiscal_localizations/brazil.html#configure-avatax-integration"
                         company_dependent="1">
                    <field name="l10n_br_avatax_api_identifier" invisible="1"/>
                    <field name="l10n_br_avatax_api_key" invisible="1"/>
                    <div class="content-group">
                        <div class="row">
                            <label string="Environment" for="l10n_br_avalara_environment"
                                   class="col-lg-3 o_light_label"/>
                            <field name="l10n_br_avalara_environment"/>
                        </div>
                        <div class="row">
                            <field name="l10n_br_avatax_show_overwrite_warning" invisible="1"/>
                            <label string="Avatax Portal Email" for="l10n_br_avatax_portal_email"
                                   class="col-lg-3 o_light_label"/>
                            <field name="l10n_br_avatax_portal_email"/>
                            <div class="mt16">
                                <button name="create_account" type="object" class="btn-link" noSaveDialog="1"
                                        invisible="not l10n_br_avatax_show_overwrite_warning"
                                        confirm="Creating a new account will permanently remove your current Avatax account from Odoo, but it won't delete the account on the Avatax side. Do you wish to proceed?">
                                    <i title="Create a new account" role="img" aria-label="Create a new account"
                                       class="fa fa-plug fa-fw"/>
                                    Create a new account
                                </button>
                                <button name="create_account" type="object" class="btn-link" noSaveDialog="1"
                                        invisible="l10n_br_avatax_show_overwrite_warning">
                                    <i title="Create account" role="img" aria-label="Create account"
                                       class="fa fa-plug fa-fw"/>
                                    Create account
                                </button>
                            </div>
                        </div>
                        <div class="mt16"
                             invisible="l10n_br_avatax_api_identifier in (False, '') or l10n_br_avatax_api_key in (False, '')">
                            <button name="button_l10n_br_avatax_ping" type="object" class="btn-link">
                                <i title="Test connection" role="img" aria-label="Test connection"
                                   class="fa fa-plug fa-fw"/>
                                Test connection
                            </button>
                        </div>
                        <div class="mt16">
                            <button name="button_l10n_br_avatax_log" type="object" class="btn-link">
                                <i title="Start logging for 30 minutes" role="img"
                                   aria-label="Start logging for 30 minutes" class="fa fa-file-text-o"/>
                                Start logging for 30 minutes
                            </button>
                            <button name="l10n_br_avatax.ir_logging_avalara_action" type="action" class="btn-link">
                                <i title="Show logs" role="img" aria-label="Show logs" class="fa fa-file-text-o"/>
                                Show logs
                            </button>
                        </div>
                        <field name="l10n_br_tax_regime" invisible="1"/>
                        <div class="row" title="This only applies if you are a simplified tax regime company."
                             invisible="l10n_br_tax_regime != 'simplified'">
                            <label for="l10n_br_icms_rate" class="col-lg-3 o_light_label"/>
                            <field name="l10n_br_icms_rate" class="w-50"/>
                        </div>
                        <div class="row" title="Main CNAE code registered with the government.">
                            <label string="CNAE Code" for="l10n_br_cnae_code_id"
                                   class="col-lg-3 o_light_label"/>
                            <field name="l10n_br_cnae_code_id" class="w-50"/>
                        </div>
                    </div>
                    <div groups="base.group_no_one">
                        <span class="o_form_label">Transfer API credentials</span>
                        <div class="text-muted">
                            Use this only when you already created an account in another Odoo instance and wish to reuse it.
                        </div>
                        <div class="content-group">
                            <div class="row">
                                <label string="API ID" for="l10n_br_avatax_api_identifier"
                                       class="col-lg-3 o_light_label"/>
                                <field name="l10n_br_avatax_api_identifier"/>
                            </div>
                            <div class="row">
                                <label string="API Key" for="l10n_br_avatax_api_key" class="col-lg-3 o_light_label"/>
                                <field name="l10n_br_avatax_api_key"/>
                            </div>
                        </div>
                    </div>
                </setting>
            </setting>
        </field>
    </record>
</odoo>
