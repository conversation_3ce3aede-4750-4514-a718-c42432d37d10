# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_repair
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "--&gt;"
msgstr ""

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-danger\">Kontrole jakości</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-success\">Kontrole jakości</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alerts</span>"
msgstr "<span class=\"o_stat_text\">Alerty jakości</span>"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text\">Kontrole jakości</span>"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_ids
msgid "Alerts"
msgstr "Ostrzeżenia"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_ids
msgid "Checks"
msgstr "Czeki"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Alert"
msgstr "Alert jakości"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_count
msgid "Quality Alert Count"
msgstr "Liczba alertów jakości"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_check
msgid "Quality Check"
msgstr "Kontrola jakości"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_fail
msgid "Quality Check Fail"
msgstr "Kontrola jakości nie powiodła się"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_todo
msgid "Quality Check Todo"
msgstr "Kontrola jakości do zrobienia"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Checks"
msgstr "Kontrole jakości"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_point
msgid "Quality Control Point"
msgstr "Punkt kontroli jakości"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_repair_order
#: model:ir.model.fields,field_description:quality_repair.field_quality_alert__repair_id
#: model:ir.model.fields,field_description:quality_repair.field_quality_check__repair_id
msgid "Repair Order"
msgstr "Zamówienia naprawy"

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with repair operation types."
msgstr ""

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/repair.py:0
msgid "You still need to do the quality checks!"
msgstr "Trzeba jeszcze przeprowadzić kontrolę jakości!"
