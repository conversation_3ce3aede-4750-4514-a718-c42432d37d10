# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_gantt
# 
# Translators:
# <PERSON><PERSON>s <<EMAIL>>, 2024
# <PERSON> <joz<PERSON>@odoo.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s is on time off %(leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s requested time off %(leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Approve"
msgstr "Patvirtinti"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr "Dienos"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Refuse"
msgstr "Atmesti"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr "Neatvykimai"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-javascript
#: code:addons/hr_holidays_gantt/static/src/views/gantt/hr_holidays_gantt_view.js:0
msgid "Time Off Request"
msgstr "Atostogų Prašymas"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Validate"
msgstr "Patvirtinti"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "from %(start_date)s to %(end_date)s"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s from %(start_time)s to %(end_time)s"
msgstr ""
