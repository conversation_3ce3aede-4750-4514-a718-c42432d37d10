# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_monster
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_28
msgid "Accounting and Auditing Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_36
msgid "Administrative and Support Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_29
msgid "Advertising and PR Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_6
msgid "Aerospace and Defense"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_1
msgid "Agriculture/Forestry/Fishing"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_0
msgid "All"
msgstr "Všetko"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_30
msgid "Architectural and Design Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid "Authentication error: Monster.com credentials are invalid."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_75
msgid "Automotive Sales and Repair Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_7
msgid "Automotive and Parts Mfg"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_23
msgid "Banking"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_8
msgid "Biotechnology/Pharmaceuticals"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_21
msgid "Broadcasting, Music, and Film"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_76
msgid "Business Services - Other"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_9
msgid "Chemicals/Petro-Chemicals"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_14
msgid "Clothing and Textile Manufacturing"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_32
msgid "Computer Hardware"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_33
msgid "Computer Software"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_77
msgid "Computer/IT Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_4
msgid "Construction - Industrial Facilities and Infrastructure"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_78
msgid "Construction - Residential & Commercial/Office"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_10
msgid "Consumer Packaged Goods Manufacturing"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_contract_type
msgid "Contract Type"
msgstr "Druh zmluvy"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Critical error: Monster.com service has changed. Please contact customer "
"support."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_currency
msgid "Currency"
msgstr "Mena"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_38
msgid "Education"
msgstr "Vzdelanie"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_11
msgid "Electronics, Components, and Semiconductor Mfg"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_3
msgid "Energy and Utilities"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_79
msgid "Engineering Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_80
msgid "Entertainment Venues and Theaters"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_81
msgid "Financial Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_82
msgid "Food and Beverage Production"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_51
msgid "Government - Military/Defense"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_52
msgid "Government - National"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_50
msgid "Government and Military"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_16
msgid "Grocery/Convenience/Gas"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_39
msgid "Healthcare Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_44
msgid "Hotels and Lodging"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_partner_industry
msgid "Industry"
msgstr "Priemysel"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_24
msgid "Insurance"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_20
msgid "Internet Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_job_post
msgid "Job Post"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_34
msgid "Legal Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_31
msgid "Management Consulting Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_35
msgid "Management and Holding Companies"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_12
msgid "Manufacturing - Other"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_83
msgid "Marine Mfg & Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_84
msgid "Medical Devices and Supplies"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_2
msgid "Metals and Minerals"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Monster Credentials"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_hr_contract_type__monster_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_currency__monster_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_partner_industry__monster_id
msgid "Monster ID"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,help:hr_recruitment_integration_monster.field_hr_contract_type__monster_id
msgid "Monster ID of the contract type."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_company__hr_recruitment_monster_username
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_config_settings__hr_recruitment_monster_username
msgid "Monster Identifier"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_company__hr_recruitment_monster_password
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_config_settings__hr_recruitment_monster_password
msgid "Monster Password"
msgstr ""

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Monster.com credentials are not set. Please set them in the company settings"
" or ask your administrator."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_25
msgid "Mortgage"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_47
msgid "Nonprofit Charitable Organizations"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_40
msgid "Nursing/Residential Care Facilities"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_85
msgid "Other/Not Classified"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Password"
msgstr "Heslo"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_42
msgid "Performing and Fine Arts"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_48
msgid "Personal and Household Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_13
msgid "Printing and Publishing "
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_26
msgid "Real Estate/Property Management"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_recruitment_platform
msgid "Recruitment Platform"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_27
msgid "Rental Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_49
msgid "Repair and Maintenance Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_45
msgid "Restaurant/Food Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_17
msgid "Retail"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_74
msgid "Security and Surveillance"
msgstr ""

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Service not available: Monster.com service is not available. Please try "
"again later."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_41
msgid "Social Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_43
msgid "Sports and Physical Recreation"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_46
msgid "Staffing/Employment Agencies"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_22
msgid "Telecommunications Services"
msgstr ""

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_job_post.py:0
msgid "This Monster.com job post is not linked to an actual job post."
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_5
msgid "Trade Contractors"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_19
msgid "Transport and Storage - Materials "
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_18
msgid "Travel, Transportation and Tourism"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Username"
msgstr "Používateľské meno"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_37
msgid "Waste Management"
msgstr ""

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_15
msgid "Wholesale Trade/Import - Export"
msgstr ""
