[options]
addons_path = {root_path}/odoo/addons,{root_path}/addons
admin_passwd = admin
csv_internal_sep = ,
data_dir = {homedir}/.local/share/Odoo
db_host = False
db_maxconn = 64
db_maxconn_gevent = False
db_name = False
db_password = False
db_port = False
db_replica_host = False
db_replica_port = False
db_sslmode = prefer
db_template = template0
db_user = False
dbfilter = 
email_from = False
from_filter = False
geoip_city_db = /usr/share/GeoIP/GeoLite2-City.mmdb
geoip_country_db = /usr/share/GeoIP/GeoLite2-Country.mmdb
gevent_port = 8072
http_enable = True
http_interface = 
http_port = 8069
import_partial = 
limit_memory_hard = 2684354560
limit_memory_hard_gevent = False
limit_memory_soft = 2147483648
limit_memory_soft_gevent = False
limit_request = 65536
limit_time_cpu = 60
limit_time_real = 120
limit_time_real_cron = -1
limit_time_worker_cron = 0
list_db = True
log_db = False
log_db_level = warning
log_handler = :INFO
log_level = info
logfile = 
max_cron_threads = 2
osv_memory_count_limit = 0
pg_path = 
pidfile = 
pre_upgrade_scripts = 
proxy_mode = False
reportgz = False
screencasts = 
screenshots = /tmp/odoo_tests
server_wide_modules = base,web
smtp_password = False
smtp_port = 25
smtp_server = localhost
smtp_ssl = False
smtp_ssl_certificate_filename = False
smtp_ssl_private_key_filename = False
smtp_user = False
syslog = False
test_enable = False
test_file = 
test_tags = None
transient_age_limit = 1.0
translate_modules = ['all']
unaccent = False
upgrade_path = 
websocket_keep_alive_timeout = 3600
websocket_rate_limit_burst = 10
websocket_rate_limit_delay = 0.2
without_demo = False
workers = 0
x_sendfile = False
