from odoo import models, fields, api
from odoo.exceptions import UserError


class SalesPriceHistoryWizard(models.TransientModel):
    _name = 'sales.price.history.wizard'
    _description = 'Sales Price History Wizard'

    part_code_id = fields.Many2one('product.part.code')
    date_from = fields.Date(
        string='Date From',
        default=fields.Date.today,
        help='Start date for the report'
    )
    date_to = fields.Date(
        string='Date To',
        default=fields.Date.today,
        help='End date for the report'
    )
    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        domain=[('is_company', '=', True), ('customer_rank', '>', 0)],
        help='Leave empty to include all customers'
    )

    def action_generate_report(self):
        """Generate the sales price history report"""
        # Find the product by code or default_code
        if self.part_code_id:
            products = self.env['product.product'].search([
                ('part_code_id', '=', self.part_code_id.id)], limit=1)
        else:
            products = self.env['product.product'].search([])

        if len(products)==0:
            raise UserError(f"No product found with code: {self.part_code_id.name}")

        # Build domain for sale order lines
        order_domain = [
            ('state', 'in', ['sale', 'done']),
            ('date_order', '>=', self.date_from),
            ('date_order', '<=', self.date_to),
        ]

        if self.customer_id:
            order_domain.append(('partner_id', '=', self.customer_id.id))

        sale_orders = self.env['sale.order'].search(order_domain)

        if not sale_orders:
            raise UserError("No sales records found for the specified criteria.")

        # Now get sale order lines for the product from these orders
        line_domain = [
            ('product_id', 'in', products.ids),
            ('order_id', 'in', sale_orders.ids),
        ]

        sale_lines = self.env['sale.order.line'].search(line_domain, order='order_id desc')

        if not sale_lines:
            raise UserError("No sales records found for the specified product in the given criteria.")

        # Create report data
        report_records = self.env['sales.price.history.report']
        for product in products:
            for line in sale_lines.filtered(lambda x:x.product_id==product):
                report_record = report_records.create({
                    'product_id': product.id,
                    'part_code_id': product.part_code_id.id or '',
                    'date_from': self.date_from,
                    'date_to': self.date_to,
                    'order_date': line.order_id.date_order,
                    'order_name': line.order_id.name,
                    'customer_id': line.order_id.partner_id.id,
                    'quantity': line.product_uom_qty,
                    'unit_price': line.price_unit,
                    'subtotal': line.price_subtotal,
                    'currency': line.order_id.currency_id.name,
                })
                report_records += report_record

        # Return action to show the report
        return {
            'type': 'ir.actions.act_window',
            'name': f'Sales Price History - {product.name}',
            'res_model': 'sales.price.history.report',
            'view_mode': 'list,form',
            'target': 'current',
            'domain': [('id', 'in', report_records.ids)],
            'context': {'create': False, 'edit': False, 'delete': True,'search_default_group_by_customer_id': 1,
                'search_default_group_by_product_id': 2,},
        }