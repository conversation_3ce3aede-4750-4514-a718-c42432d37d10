<?xml version="1.0" encoding="utf-8"?>
<odoo>`
    <record id="view_stock_quant_tree_inherit" model="ir.ui.view">
        <field name="name">stock.quant.list.inherit</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="after">
                <field name="product_condition_id" optional="show" options="{'no_open': True}" readonly="1"/>
            </xpath>
        </field>
    </record>`

    <record id="view_stock_quant_tree_simple_inherit" model="ir.ui.view">
        <field name="name">stock.quant.list.inherit</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_simple"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="after">
                <field name="product_condition_id" optional="show" options="{'no_open': True}" readonly="1"/>
            </xpath>
        </field>
    </record>
</odoo>